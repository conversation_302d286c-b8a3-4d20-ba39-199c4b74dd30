{"name": "@obs/obs-react-library", "version": "10.0.0", "description": "OBS react library", "author": "<PERSON> <<EMAIL>>", "homepage": "", "license": "ISC", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/types/index.d.ts", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["dist"], "scripts": {"build": "yarn tsc", "tsc": "tsc -p . --outDir ./dist", "prepare": "yarn build"}, "publishConfig": {"access": "public"}, "devDependencies": {"@types/react": "^19.1.4", "react": "^19.1.0", "react-dom": "^19.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"@obs/obs-library": "latest", "@stencil/react-output-target": "^1.0.2"}, "gitHead": "4744d8f3dbe899b4213e5b3887d444f016d4166b"}