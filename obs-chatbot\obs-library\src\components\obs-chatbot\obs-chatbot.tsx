import { Component, h, Host, Prop, Event, EventEmitter, State, Fragment, Watch } from '@stencil/core';
import '@jamescoyle/svg-icon';
import { mdiClose, mdiArrowUp, mdiRobotHappy, mdiRobot } from '@mdi/js';
import { THEME } from '../../core/enums/theme';
import { CHAT_ROLES } from '../../core/enums/chatRoles';
import { IMessage } from '../../core/types/message';
import APIInstance from '../../plugins/axiosInstance';
import { store } from '../../stores/chatbotStore';
import 'highlight.js/styles/tokyo-night-dark.css';
import { sanitizeComplexeSentence, sanitizeSentence } from '../../plugins/marked';
import { PROVIDER } from '../../core/enums/provider';
import { getStrategy } from '../../core/services/completionsService';

/**
 * Display a chatbot
 */
@Component({
  tag: 'obs-chatbot',
  styleUrl: 'obs-chatbot.scss',
  shadow: true,
})
export class ObsChatbot {
  @Prop() baseUrl: string;
  @Prop() apiVersion: string = '';
  @Prop() open: boolean;
  @Prop() initialValue: string = '';
  @Prop() theme: THEME = THEME.AUTO;
  @Prop() placeholder: string = 'Type your message here...';
  @Prop() startMessage: string = "Hello ! I'm your virtual assistant !";
  @Prop() errorMessage: string = "I'm sorry, an error occured. Please, try later.";
  @Prop() systemMessage: string = 'You are a virtual assistant.';
  @Prop() model: string = 'llama-3-8b-instruct';
  @Prop() temperature: number = 0.3;
  @Prop() max_tokens: number = 1000;
  @Prop() top_p: number = 0.9;
  @Prop() frequency_penalty: number = 0.5;
  @Prop() presence_penalty: number = 0.0;
  @Prop() stream: boolean = true;
  @Prop() provider: PROVIDER = PROVIDER.LIVEINTILLIGENCE;
  @Prop() workspace_ids: number[] = [1];
  @Prop() company_scope: boolean = false;
  @Prop() private_scope: boolean = true;
  @Prop() tool: string = 'DocumentSearch';

  @Event() closedChatbot: EventEmitter;

  @State() inputValue: string = '';
  @State() systemDarkMode: boolean;
  @State() mediaQueryList: MediaQueryList;
  @State() messages: Array<IMessage> = [
    {
      role: CHAT_ROLES.SYSTEM,
      content: this.systemMessage,
    },
  ];
  @State() isLoading: boolean = false;
  @State() writingMessage: string = store.getState().writingMessage;

  private apiInstance: APIInstance;
  private inputRef: HTMLTextAreaElement;
  private unsubscribeWritingMessage: () => void;
  private unsubscribeIsLoading: () => void;
  private unsubscribeIsError: () => void;
  private unsubscribeIsWriting: () => void;

  @Watch('open')
  openWatch() {
    this.focusInput();
    if (this.open) {
      this.resetComponent();
    }
  }

  focusInput() {
    this.inputRef?.focus();
  }

  connectedCallback() {
    this.apiInstance = new APIInstance(this.baseUrl, this.apiVersion);
    this.mediaQueryList = window.matchMedia('(prefers-color-scheme: dark)');
    this.updateMediaQuery(this.mediaQueryList);
    this.mediaQueryList.addEventListener('change', this.updateMediaQuery);

    this.unsubscribeWritingMessage = store.subscribe('writingMessage', newState => {
      this.writingMessage = newState;
    });

    this.unsubscribeIsWriting = store.subscribe('isWriting', newState => {
      if (!newState && this.writingMessage) {
        this.messages = [...this.messages, { role: CHAT_ROLES.ASSISTANT, content: this.writingMessage }];
        store.setState({ writingMessage: '' });
        this.focusInput();
      }
    });

    this.unsubscribeIsLoading = store.subscribe('isLoading', newState => {
      this.isLoading = newState;
    });

    this.unsubscribeIsError = store.subscribe('isError', newState => {
      if (newState) this.messages = [...this.messages, { role: CHAT_ROLES.ASSISTANT, content: this.errorMessage }];
    });
  }

  componentWillLoad() {
    this.inputValue = this.initialValue;
  }

  disconnectedCallback() {
    this.mediaQueryList.removeEventListener('change', this.updateMediaQuery);
    this.unsubscribeWritingMessage();
    this.unsubscribeIsLoading();
    this.unsubscribeIsError();
    this.unsubscribeIsWriting();
  }

  private readonly updateMediaQuery = (event: MediaQueryListEvent | MediaQueryList) => {
    this.systemDarkMode = event.matches;
  };

  private readonly getTheme = () => {
    if (this.theme === THEME.AUTO) {
      if (window.matchMedia) {
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? THEME.DARK : THEME.LIGHT;
      } else return THEME.LIGHT;
    }
    return this.theme;
  };

  private readonly handleCloseChatbot = () => {
    this.closedChatbot.emit();
    this.resetComponent();
  };

  private readonly handleInputChange = (event: InputEvent) => {
    this.inputRef.style.height = 'auto';
    this.inputRef.style.height = `${this.inputRef.scrollHeight}px`;
    if (!this.isLoading) {
      const target = event.target as HTMLInputElement;
      this.inputValue = target.value;
    }
  };

  private readonly handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      this.handleSubmit();
    }

    if (event.key === 'Enter' && event.shiftKey) {
      this.inputValue = this.inputValue + '\n';
    }
  };

  private readonly handleSubmit = async () => {
    if (!this.isLoading && this.inputValue) {
      const answer = this.inputValue;
      this.inputValue = '';

      this.messages = [
        ...this.messages,
        {
          role: CHAT_ROLES.USER,
          content: answer,
        },
      ];

      const strategy = getStrategy(this.provider);

      strategy.chat(this.apiInstance.api, {
        model: this.model,
        messages: this.messages,
        temperature: this.temperature,
        max_tokens: this.max_tokens,
        top_p: this.top_p,
        frequency_penalty: this.frequency_penalty,
        presence_penalty: this.presence_penalty,
        stream: this.stream,
        workspace_ids: this.workspace_ids,
        company_scope: this.company_scope,
        private_scope: this.private_scope,
        tool: this.tool,
      });
    }
  };

  resetComponent = () => {
    this.inputValue = this.initialValue;
    this.messages = [
      {
        role: CHAT_ROLES.SYSTEM,
        content: this.systemMessage,
      },
    ];
  };

  render() {
    return (
      <Host>
        <div class={`obs--container ${this.open ? 'open' : ''}`} data-theme={this.getTheme()}>
          <div class="obs--prepend">
            <div class="row">
              <div class="col">
                <button class="obs--container-header-close" onClick={() => this.handleCloseChatbot()}>
                  <slot name="close-icon">
                    <span class="mdi mdi-close"></span>
                    <svg-icon type="mdi" path={mdiClose}></svg-icon>
                  </slot>
                </button>
              </div>
            </div>
          </div>
          <div class="obs--content">
            <auto-scroll content={this.messages.join(',')}>
              <div class="row">
                <div class="col-12 obs--chat-bot">{this.startMessage}</div>
                {this.messages.map(x => {
                  if (x.role === CHAT_ROLES.ASSISTANT) return <div class="col-12 obs--chat-bot" innerHTML={sanitizeComplexeSentence(x.content)} />;
                  else if (x.role === CHAT_ROLES.USER)
                    return (
                      <div class={`col-8 offset-4 ${this.messages.findLast(y => y.role === CHAT_ROLES.USER) === x ? 'stop-scroll' : ''}`}>
                        <div class={`obs--chat-human p-2`} innerHTML={sanitizeSentence(x.content).replace(/\n/g, '<br />')} />
                      </div>
                    );
                })}
                {this.writingMessage && <div class="col-12 obs--chat-bot" innerHTML={sanitizeComplexeSentence(this.writingMessage)} />}
              </div>
            </auto-scroll>
          </div>
          <div class="obs--icon">
            <div class="row">
              <div class="col d-flex">
                <slot name="icone">
                  {!this.isLoading && <svg-icon type="mdi" path={mdiRobotHappy} />}
                  {this.isLoading && (
                    <Fragment>
                      <svg-icon type="mdi" path={mdiRobot} class="mr-1" />
                      <loader-dots />
                    </Fragment>
                  )}
                </slot>
              </div>
            </div>
          </div>
          <div class="obs--append">
            <div class="row">
              <div class="col ">
                <div class="obs--textfield-container p-2">
                  <textarea
                    autoFocus
                    ref={el => (this.inputRef = el)}
                    disabled={this.isLoading}
                    class="obs--textfield-textarea"
                    placeholder={this.placeholder}
                    value={this.inputValue}
                    onInput={event => this.handleInputChange(event)}
                    onKeyDown={event => this.handleKeyDown(event)}
                  />
                  <div class="obs--textfield-button-container">
                    <button disabled={this.isLoading} class="obs--textfield-button obs--textfield-button-icon" onClick={() => this.handleSubmit()}>
                      <svg-icon type="mdi" path={mdiArrowUp} />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class={`obs--scrim ${this.open ? 'open' : ''}`} />
      </Host>
    );
  }
}
