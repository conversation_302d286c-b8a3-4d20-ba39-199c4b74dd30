@obs:registry=https://pkgs.dev.azure.com/{{organisation_name}}/_packaging/{{organisation_name}}/npm/registry/
always-auth=true

; begin auth token
//pkgs.dev.azure.com/{{organisation_name}}/_packaging/{{organisation_name}}/npm/registry/:username={{organisation_name}}
//pkgs.dev.azure.com/{{organisation_name}}/_packaging/{{organisation_name}}/npm/registry/:_password={{password}}
//pkgs.dev.azure.com/{{organisation_name}}/_packaging/{{organisation_name}}/npm/registry/:email=npm requires email to be set but doesn't use the value
//pkgs.dev.azure.com/{{organisation_name}}/_packaging/{{organisation_name}}/npm/:username={{organisation_name}}
//pkgs.dev.azure.com/{{organisation_name}}/_packaging/{{organisation_name}}/npm/:_password={{password}}
//pkgs.dev.azure.com/{{organisation_name}}/_packaging/{{organisation_name}}/npm/:email=npm requires email to be set but doesn't use the value
; end auth token