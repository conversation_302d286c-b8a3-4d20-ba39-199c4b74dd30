$max-gap: 16;

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  z-index: 1;

  &-track {
    background: transparent;
  }
  &-thumb {
    background: var(--obs-scroll-color);
    opacity: 0.5;
    border-radius: 100px;
  }
}
html,
body * {
  scrollbar-color: var(--obs-scroll-color) transparent;
  scrollbar-width: thin;
}

.m {
  @for $i from 1 through $max-gap {
    $gap: calc(#{$i} * 4px);
    &-#{$i} {
      margin: $gap;
    }
    &x-#{$i} {
      margin-left: $gap;
      margin-right: $gap;
    }
    &y-#{$i} {
      margin-top: $gap;
      margin-bottom: $gap;
    }
    &r-#{$i} {
      margin-right: $gap;
    }
    &l-#{$i} {
      margin-left: $gap;
    }
    &t-#{$i} {
      margin-top: $gap;
    }
    &b-#{$i} {
      margin-bottom: $gap;
    }
  }
}

.p {
  @for $i from 1 through $max-gap {
    $gap: calc(#{$i} * 4px);
    &-#{$i} {
      padding: $gap;
    }
    &x-#{$i} {
      padding-left: $gap;
      padding-right: $gap;
    }
    &y-#{$i} {
      padding-top: $gap;
      padding-bottom: $gap;
    }
    &r-#{$i} {
      padding-right: $gap;
    }
    &l-#{$i} {
      padding-left: $gap;
    }
    &t-#{$i} {
      padding-top: $gap;
    }
    &b-#{$i} {
      padding-bottom: $gap;
    }
  }
}
.d-flex {
  display: flex;
}
.align-items-center {
  align-items: center;
}
