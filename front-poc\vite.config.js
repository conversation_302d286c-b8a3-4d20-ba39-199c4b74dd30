import { fileURLToPath, URL } from "node:url";

import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import vueDevTools from "vite-plugin-vue-devtools";

// https://vite.dev/config/
const vuePluginConfig = vue({
  template: {
    compilerOptions: {
      isCustomElement: (tag) => tag.startsWith("obs-"),
    },
  },
});
const vueServerConfig = {
  port: 8080,
  strictPort: true,
  host: true,
  origin: "http://0.0.0.0:8080",
};
const vuePreviewConfig = {
  port: 8080,
  strictPort: true,
  host: true,
  origin: "http://0.0.0.0:8080",
  allowedHosts: [
    "trust.pprd.liveintelligence.orange-business.com"
  ]
};
const resolveConfig = {
  alias: {
    "@": fileURLToPath(new URL("./src", import.meta.url)),
  },
};

const vuePlugins = [vuePluginConfig];

export default defineConfig(({ command }) => {
  if (command === "serve") {
    vuePlugins.push(vueDevTools());
  }

  return {
    base: "/chatbot/",
    plugins: vuePlugins,
    resolve: resolveConfig,
    server: vueServerConfig,
    preview: vuePreviewConfig,
  };
});
