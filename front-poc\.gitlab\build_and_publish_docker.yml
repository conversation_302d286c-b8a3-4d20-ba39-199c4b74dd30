variables:
    DOCKER_REPO: genai_saas_product/business-apps/chatbot
    DOCKER_IMAGE_NAME: live-intelligence-chatbot

include:
    - project: "genai-product/pipeline-templates"
      ref: main
      file: "plug_and_play/build_and_publish_docker_image.yml"

.setup_build_prerequisites:
    before_script:
        - mkdir -p /run/secrets
        - mv .env.local /run/secrets/env

setup_job:
    stage: setup
    extends:
        - .read_vault_path
    artifacts:
        paths:
            - .env.local
        expire_in: 1 hour
    script:
        - apk add --update --no-cache jq
        - VAULT_VALUES="$(vault kv get -format json /prj_genai-saas-product/chatbot)"
        - |
            {
              echo "ORGANISATION_NAME=$(echo ${VAULT_VALUES} | jq -r '.data.data.organisation_name')"
              echo "PASSWORD=$(echo ${VAULT_VALUES} | jq -r '.data.data.password')"
              echo "VITE_APP_OPENID_AUTHORITY=$(echo ${VAULT_VALUES} | jq -r '.data.data.openid_authority')"
              echo "VITE_APP_OPENID_CLIENT_ID=$(echo ${VAULT_VALUES} | jq -r '.data.data.openid_clientid')"
              echo "VITE_APP_HOST_NAME=$(echo ${VAULT_VALUES} | jq -r '.data.data.host_name')"
              echo "VITE_BACKEND_HOST_NAME=$(echo ${VAULT_VALUES} | jq -r '.data.data.backend_host_name')"
              echo "VITE_BACKEND_API_PATH=$(echo ${VAULT_VALUES} | jq -r '.data.data.backend_api_path')"
              echo "VITE_MODEL_NAME"=$(echo ${VAULT_VALUES} | jq -r '.data.data.model_name')
            } > .env.local

build_docker_image_job:
  dependencies:
    - setup_job
    - extract_gitversion
  extends:
    - .setup_build_prerequisites
    - .build_docker_image_job

publish_docker_image_job:
  dependencies:
    - setup_job
    - extract_gitversion
  extends:
    - .setup_build_prerequisites
    - .publish_docker_image_job