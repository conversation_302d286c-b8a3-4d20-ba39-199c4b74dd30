# `@obs/obs-react-library`

OBS Library for ract

## Usage

```tsx
// src/App.tsx
import { useState } from "react";
import { ObsChatbot } from "@obs/obs-react-library";

function App() {
  const [open, setOpen] = useState(false);

  return (
    <>
      <button onClick={() => setOpen(true)}>Open</button>
      <ObsChatbot open={open} onClosedChatbot={() => setOpen(false)} />
    </>
  );
}

export default App;
```
