import { createRouter, createWebHistory } from "vue-router";
import mainOidc from "@/services/SecurityService";

const HomePage = () => import("@/pages/home-page.vue");
const ChatbotPage = () => import("@/pages/chatbot-page.vue");
const NotFoundPage = () => import("@/pages/not-found-page.vue");

const routes = [
  {
    path: "/",
    name: "home",
    component: HomePage,
  },
  {
    path: "/chatbot",
    name: "chatbot",
    component: ChatbotPage,
    beforeEnter: [requiresAuth],
  },
  {
    path: "/:catchAll(.*)",
    name: "Not Found",
    component: NotFoundPage,
  },
];

function requiresAuth(to, from, next) {
  if (!mainOidc.isAuthenticated) {
    next({ name: "home" });
  } else {
    next();
  }
}

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

mainOidc.useRouter(router);

export default router;
