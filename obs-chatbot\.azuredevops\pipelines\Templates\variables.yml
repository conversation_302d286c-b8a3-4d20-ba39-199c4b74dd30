variables:
  # Build environment variables
  - name: vmImage
    value: ubuntu-latest

  # Project location and outputs
  - name: AppName
    value: live_intelligence_webcomponents

  #Artifact Name
  - name: webBuildArtifactName
    value: web_build
  - name: webArtifactName
    value: web_ci

  # Project location and outputs
  # npm - Set package cache folder
  - name: yarn_config_cache
    value: $(Pipeline.Workspace)/.yarn

  # Variable group for Npm Registry
  - group: FRONT_NPM_REGISTRY
