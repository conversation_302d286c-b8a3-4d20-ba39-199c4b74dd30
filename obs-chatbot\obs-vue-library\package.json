{"name": "@obs/obs-vue-library", "version": "10.0.0", "description": "Obs vue library", "author": "<PERSON> <<EMAIL>>", "homepage": "", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "directories": {"lib": "lib", "test": "__tests__"}, "files": ["dist"], "scripts": {"build": "yarn tsc", "tsc": "tsc -p . --outDir ./dist", "prepare": "yarn build"}, "publishConfig": {"access": "public"}, "dependencies": {"@obs/obs-library": "latest", "@stencil/vue-output-target": "^0.10.7", "ts-node": "^10.9.2", "typescript": "^5.8.3", "vue": "3.5.13"}, "packageManager": "yarn@1.22.22", "gitHead": "4744d8f3dbe899b4213e5b3887d444f016d4166b"}