apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chatbot-back-poc-ingress
  annotations:
    cert-manager.io/issuer: "letsencrypt-liveintelligence"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
  labels:
    app: chatbot-back-poc
spec:
  ingressClassName: nginx
  tls:
    - hosts:
      - trust.pprd.liveintelligence.orange-business.com
      secretName: chatbot.liveintelligence.orange-business.com-tls
  rules:
    - host: trust.pprd.liveintelligence.orange-business.com
      http:
        paths:
          - path: /proxy(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: chatbot-back-poc
                port:
                  number: 8080

