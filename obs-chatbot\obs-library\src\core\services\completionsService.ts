import { AxiosInstance } from 'axios';
import { ICompletionRequest, ICompletionResponse } from '../types/completion';
import { EventStreamContentType, fetchEventSource } from '@microsoft/fetch-event-source';
import { store } from '../../stores/chatbotStore';
import type { IDocSearchCompletionRequest, IDocSearchCompletionResponse, Strategy } from '../types/provider';
import { PROVIDER } from '../enums/provider';

class RetriableError extends Error {}
class FatalError extends Error {}

export class LiveIntelligenceProvider implements Strategy {
  async chat(apiInstance: AxiosInstance, chat: ICompletionRequest) {
    if (chat.stream) {
      callStream(apiInstance.getUri(), chat);
    } else {
      store.setState({ isLoading: true });
      store.setState({ isWriting: true });

      let res: ICompletionResponse = undefined;

      try {
        const response = await apiInstance.post<ICompletionResponse>('/chat/completions', chat);
        res = response.data;

        store.setState({ isError: false });
        store.setState({ writingMessage: res.choices[0].message.content });
      } catch (error) {
        store.setState({ isError: true });
        console.error(error);
      }
      store.setState({ isLoading: false });
      store.setState({ isWriting: false });
    }
  }
}

export class DocSearchProvider implements Strategy {
  async chat(apiInstance: AxiosInstance, chat: ICompletionRequest) {
    const newChat: IDocSearchCompletionRequest = {
      query: chat.messages[chat.messages.length - 1].content,
      model: chat.model,
      workspace_ids: chat.workspace_ids,
      company_scope: chat.company_scope,
      private_scope: chat.private_scope,
      tool: chat.tool,
    };

    store.setState({ isLoading: true });
    store.setState({ isWriting: true });

    let res: IDocSearchCompletionResponse = undefined;

    try {
      const response = await apiInstance.post<IDocSearchCompletionResponse>('/document-search', newChat);
      res = response.data;

      store.setState({ isError: false });
      store.setState({ writingMessage: res.content });
    } catch (error) {
      store.setState({ isError: true });
      console.error(error);
    }
    store.setState({ isLoading: false });
    store.setState({ isWriting: false });
  }
}

/**
 * Stream
 * @param chat
 */
const callStream = (url: string, chat: ICompletionRequest) => {
  let str = '';
  store.setState({ isLoading: true });
  store.setState({ isError: false });
  store.setState({ isWriting: false });
  store.setState({ writingMessage: '' });

  fetchEventSource(`${url}/chat/completions/stream`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(chat),
    async onopen(response) {
      if (response.ok && response.headers.get('content-type') === EventStreamContentType) {
        return;
      } else if (response.status >= 400 && response.status < 500 && response.status !== 429) {
        throw new FatalError();
      } else {
        throw new RetriableError();
      }
    },
    onmessage(msg) {
      try {
        store.setState({ isLoading: false });
        store.setState({ isWriting: true });
        const mess = JSON.parse(msg.data).content;
        if (mess) str += mess;
        store.setState({ writingMessage: str });
      } catch (error) {
        console.error(error);
        throw new FatalError(msg.data);
      }
      if (msg.event === 'FatalError') {
        throw new FatalError(msg.data);
      }
    },
    onclose() {
      store.setState({ isWriting: false });
    },
    onerror(err) {
      store.setState({ isLoading: false });
      store.setState({ isError: true });
      throw err;
    },
  });
};

const strategyRegistry: Record<PROVIDER, Strategy> = {
  [PROVIDER.LIVEINTILLIGENCE]: new LiveIntelligenceProvider(),
  [PROVIDER.DOCSEARCH]: new DocSearchProvider(),
};

export const getStrategy = (key: PROVIDER): Strategy => {
  return strategyRegistry[key];
};
