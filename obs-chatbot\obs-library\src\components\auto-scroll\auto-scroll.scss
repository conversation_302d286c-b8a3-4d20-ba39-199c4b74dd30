@use '../../global/utils.scss';

$obsAutoScrollBackgroud: var(--obs-auto-scroll-backgroud, #ffffff);
$obsAutoScrollColor: var(--obs-auto-scroll-color, #333333);
$obsAutoScrollButtonToBottomPlacement: var(--obs-auto-scroll-button-to-bottom-placement, 150px);

:host {
  display: block;
  height: 100%;
}

.scroll-container {
  max-height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.scroll-button {
  position: fixed;
  bottom: $obsAutoScrollButtonToBottomPlacement;
  left: 50%;
  transform: translateX(-50%);
  padding: 5px;
  background-color: $obsAutoScrollBackgroud;
  color: $obsAutoScrollColor;
  box-shadow: 0 4px 13px rgba(0, 0, 0, 0.2);
  border: none;
  border-radius: 50%;
  cursor: pointer;
}
