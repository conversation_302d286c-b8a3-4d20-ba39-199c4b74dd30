import { faker } from '@faker-js/faker/.';
import { IMessage, IMessageChoice } from '../types/message';
import { CHAT_ROLES } from '../enums/chatRoles';

export const createMessageMock = (): IMessage => ({
  content: faker.word.words({ count: 50 }),
  role: faker.helpers.enumValue(CHAT_ROLES),
});

export const createMessageChoiceMock = (): IMessageChoice => ({
  index: faker.number.int(),
  message: createMessageMock(),
  logprobs: null,
  finish_reason: faker.hacker.adjective(),
  stop_reason: faker.hacker.adjective(),
  id: faker.string.uuid(),
});

export const MessagesMock = faker.helpers.multiple<IMessage>(createMessageMock);
