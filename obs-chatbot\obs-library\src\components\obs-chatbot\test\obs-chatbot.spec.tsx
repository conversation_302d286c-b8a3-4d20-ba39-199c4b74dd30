import { newSpecPage } from '@stencil/core/testing';
import { ObsChatbot } from '../obs-chatbot';
import { CHAT_ROLES } from '../../../core/enums/chatRoles';

describe('obs-chatbot', () => {
  it('should emit "close" chatbot', async () => {
    const page = await newSpecPage({
      components: [ObsChatbot],
      html: `<obs-chatbot open="true" theme="light" />`,
    });

    const component = page.rootInstance;
    const spy = jest.fn();
    component.closedChatbot = { emit: spy } as any;

    component.handleCloseChatbot();

    expect(spy).toHaveBeenCalledTimes(1);
  });

  it('should change', async () => {
    const page = await newSpecPage({
      components: [ObsChatbot],
      html: `<obs-chatbot open="true" />`,
    });

    const component = page.rootInstance;

    component.handleInputChange({ target: { value: 'test' } });

    expect(component.inputValue).toEqualText('test');
  });

  it('should submit', async () => {
    const page = await newSpecPage({
      components: [ObsChatbot],
      html: `<obs-chatbot open="true" />`,
    });

    const component = page.rootInstance;

    component.handleInputChange({ target: { value: 'test' } });
    component.handleSubmit();

    expect(component.messages).toStrictEqual([
      { role: 'system', content: 'You are a virtual assistant.' },
      { role: 'user', content: 'test' },
    ]);
  });

  it('should submit when enter', async () => {
    const page = await newSpecPage({
      components: [ObsChatbot],
      html: `<obs-chatbot open="true" />`,
    });

    const component = page.rootInstance;

    const spy = jest.spyOn(component, 'handleSubmit');
    component.handleKeyDown({ key: 'Enter' });

    expect(spy).toHaveBeenCalledTimes(1);
  });

  it('should add backspace when shift+enter', async () => {
    const page = await newSpecPage({
      components: [ObsChatbot],
      html: `<obs-chatbot open="true" />`,
    });

    const component = page.rootInstance;

    component.handleKeyDown({ key: 'Enter', shiftKey: true });

    expect(component.inputValue).toStrictEqual('\n');
  });

  it('should reset component', async () => {
    const page = await newSpecPage({
      components: [ObsChatbot],
      html: `<obs-chatbot open="true" />`,
    });

    const component = page.rootInstance;

    component.resetComponent();

    expect(component.inputValue).toStrictEqual(component.initialValue);
    expect(component.messages).toStrictEqual([
      {
        role: CHAT_ROLES.SYSTEM,
        content: component.systemMessage,
      },
    ]);
  });
});
