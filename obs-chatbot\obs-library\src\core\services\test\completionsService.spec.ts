import { fetchEventSource } from '@microsoft/fetch-event-source';
import APIInstance from '../../../plugins/axiosInstance';
import { store } from '../../../stores/chatbotStore';
import { createCompletionRequestMock, createCompletionResponseMock } from '../../mock/completionsMock';
import MockAdapter from 'axios-mock-adapter';
import { PROVIDER } from '../../enums/provider';
import { getStrategy } from '../completionsService';

export async function listenToSSE(url: string, onMessage: (data: string) => void) {
  await fetchEventSource(url, {
    async onopen(_response) {},
    onmessage(event) {
      onMessage(event.data);
    },
    onclose() {},
  });
}

let mock: MockAdapter = undefined;

describe('completionService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should chat sync', async () => {
    const apiInstance = new APIInstance('http://localhost', 'api');
    const request = { ...createCompletionRequestMock(), stream: false };
    const response = createCompletionResponseMock();
    mock = new MockAdapter(apiInstance.api);
    mock.onPost('/chat/completions', request).reply(200, response);

    const strategy = getStrategy(PROVIDER.LIVEINTILLIGENCE);

    await strategy.chat(apiInstance.api, request);

    expect(store.getState().writingMessage).toEqual(response.choices[0].message.content);
  });

  it('should chat async', async () => {
    const apiInstance = new APIInstance('http://localhost', 'api');
    const request = { ...createCompletionRequestMock(), stream: true };
    const response = createCompletionResponseMock();

    const mockOnMessage = jest.fn();

    (fetchEventSource as jest.Mock).mockImplementation(async (_url, options) => {
      options?.onmessage?.({ data: JSON.stringify(response) } as MessageEvent);
    });

    await listenToSSE(apiInstance.api.getUri(), mockOnMessage);

    const strategy = getStrategy(PROVIDER.LIVEINTILLIGENCE);

    await strategy.chat(apiInstance.api, request);

    expect(mockOnMessage).toHaveBeenCalledWith(JSON.stringify(response));
  });
});
