import { faker } from '@faker-js/faker/.';
import { ICompletionRequest, ICompletionResponse } from '../types/completion';
import { createMessageChoiceMock, createMessageMock } from './messageMock';
import { IMessage, IMessageChoice } from '../types/message';

export const createCompletionRequestMock = (): ICompletionRequest => ({
  model: faker.hacker.adjective(),
  frequency_penalty: faker.number.float({ min: -2, max: 2, multipleOf: 0.1 }),
  max_tokens: faker.number.int({ min: 0, max: 6000 }),
  messages: faker.helpers.multiple<IMessage>(createMessageMock),
  presence_penalty: faker.number.float({ min: -2, max: 2, multipleOf: 0.1 }),
  stream: faker.datatype.boolean(),
  temperature: faker.number.float({ min: 0, max: 2, multipleOf: 0.1 }),
  top_p: faker.number.float({ min: 0, max: 2, multipleOf: 0.1 }),
});

export const createCompletionResponseMock = (): ICompletionResponse => ({
  id: faker.string.uuid(),
  created: faker.date.past().getTimezoneOffset(),
  model: faker.hacker.adjective(),
  object: faker.hacker.adjective(),
  prompt_logprobs: null,
  usage: {
    completion_tokens: faker.number.int(),
    prompt_tokens: faker.number.int(),
    total_tokens: faker.number.int(),
  },
  choices: faker.helpers.multiple<IMessageChoice>(createMessageChoiceMock),
});
