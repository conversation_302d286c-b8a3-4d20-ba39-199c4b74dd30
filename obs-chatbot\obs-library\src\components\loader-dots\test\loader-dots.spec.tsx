import { newSpecPage } from '@stencil/core/testing';
import { LoaderDots } from '../loader-dots';

describe('loader-dots', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [LoaderDots],
      html: `<loader-dots></loader-dots>`,
    });
    expect(page.root).toEqualHtml(`
      <loader-dots>
        <mock:shadow-root>
          <div class="chat-bubble d-flex">
            <div class="ball-1 circle"></div>
            <div class="ball-2 circle"></div>
            <div class="ball-3 circle"></div>
          </div>
        </mock:shadow-root>
      </loader-dots>
    `);
  });
});
