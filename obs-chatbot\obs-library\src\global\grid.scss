$grid-columns: 12;
$grid-gutter: 1rem;
$half-gutter: calc($grid-gutter / 2);

.row {
  display: flex;
  flex-wrap: wrap;
  padding: $half-gutter;
}

.col {
  padding: $half-gutter;
  box-sizing: border-box;
  flex: 1 1 0;

  @for $i from 1 through $grid-columns {
    &-#{$i} {
      padding: $half-gutter;
      box-sizing: border-box;
      flex: 0 0 calc(100% / #{$grid-columns} * #{$i});
    }
  }
}

.offset {
  margin-left: 0; // Reset par défaut

  @for $i from 1 through $grid-columns {
    &-#{$i} {
      margin-left: calc(100% / #{$grid-columns} * #{$i});
    }
  }
}
