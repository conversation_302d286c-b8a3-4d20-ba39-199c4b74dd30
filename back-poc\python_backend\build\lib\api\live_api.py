"""Global implementation of Paradigm's API for Live Intelligence and our LLM Quality use-cases."""

import abc
import requests
from typing import Dict, List, Optional


class ParadigmAPI(abc.ABC):
    """
    Abstract base class for Paradigm API interactions.
    All API endpoints require a Bearer API key for authentication, a base URL, and most require a model name.
    """

    def __init__(self, api_key: str, base_url: str, default_model: str = None):
        """
        Initialize the API client with authentication, base URL and default model.

        Args:
            api_key (str): The API key for authentication
            base_url (str): The base URL for the API endpoints
            default_model (str, optional): Default model to use for requests
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip("/")  # Remove trailing slash if present
        self.default_model = default_model
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }

    def _make_request(
        self, method: str, endpoint: str, data: Dict = None, params: Dict = None
    ) -> Dict:
        """
        Make an HTTP request to the Paradigm API.

        Args:
            method (str): HTTP method (GET, POST, etc.)
            endpoint (str): API endpoint path
            data (Dict, optional): Request payload
            params (Dict, optional): Query parameters

        Returns:
            Dict: API response
        """
        url = f"{self.base_url}{endpoint}"

        if method.upper() == "GET":
            response = requests.get(
                url, headers=self.headers, params=params, verify=False
            )
        elif method.upper() == "POST":
            response = requests.post(url, headers=self.headers, json=data, verify=False)
        elif method.upper() == "PUT":
            response = requests.put(url, headers=self.headers, json=data, verify=False)
        elif method.upper() == "PATCH":
            response = requests.patch(
                url, headers=self.headers, json=data, verify=False
            )
        else:
            raise ValueError(f"Unsupported HTTP method: {method}")

        response.raise_for_status()

        if response.content:
            return response.json()
        return {}

    def _ensure_model(self, model: str = None) -> str:
        """
        Ensure a model is specified, using the default if none is provided.

        Args:
            model (str, optional): Model name to use

        Returns:
            str: The model name to use

        Raises:
            ValueError: If no model is specified and no default is set
        """
        model = model or self.default_model
        if not model:
            raise ValueError("No model specified and no default model set")
        return model

    @abc.abstractmethod
    def execute(self, *args, **kwargs):
        """
        Execute the specific API workflow.
        Must be implemented by subclasses.
        """
        pass


class ChatCompletions(ParadigmAPI):
    """Implementation for chat completions API endpoint."""

    def execute(
        self,
        messages: List[Dict[str, str]],
        model: str = None,
        history: List[Dict[str, str]] = None,
        **kwargs,
    ) -> Dict:
        """
        Generate chat completions from a Large Language Model.

        Args:
            messages (List[Dict[str, str]]): List of message objects with role and content
            model (str, optional): Model to use, defaults to the instance default model
            history (List[Dict[str, str]], optional): Previous conversation history to prepend to messages
            **kwargs: Additional parameters to pass to the API

        Returns:
            Dict: API response
        """
        model = self._ensure_model(model)

        # Combine history with new messages if history is provided
        combined_messages = []
        if history:
            combined_messages.extend(history)
        combined_messages.extend(messages)

        data = {"model": model, "messages": combined_messages, **kwargs}

        return self._make_request("POST", "/api/v2/chat/completions", data)


class Completions(ParadigmAPI):
    """Implementation for text completions API endpoint."""

    def execute(self, prompt: str, model: str = None, **kwargs) -> Dict:
        """
        Generate text completions from a Large Language Model.

        Args:
            prompt (str): The prompt to complete
            model (str, optional): Model to use, defaults to the instance default model
            **kwargs: Additional parameters to pass to the API

        Returns:
            Dict: API response
        """
        model = self._ensure_model(model)

        data = {"model": model, "prompt": prompt, **kwargs}

        return self._make_request("POST", "/api/v2/completions", data)


class DocumentSearch(ParadigmAPI):
    """Implementation for document search API endpoint."""

    def execute(
        self,
        query: str,
        model: str = None,
        workspace_ids: List[int] = None,
        file_ids: List[int] = None,
        chat_session_id: int = None,
        company_scope: bool = None,
        private_scope: bool = None,
        tool: str = "DocumentSearch",
    ) -> Dict:
        """
        Search documents and generate responses.

        Args:
            query (str): The search query
            model (str, optional): Model to use, defaults to the instance default model
            workspace_ids (List[int], optional): List of workspace IDs to search
            file_ids (List[int], optional): List of document IDs to search
            chat_session_id (int, optional): Chat session ID for follow-up
            company_scope (bool, optional): Include documents from company collection
            private_scope (bool, optional): Include documents from private collection
            tool (str, optional): Tool to use, either 'DocumentSearch' or 'VisionDocumentSearch'

        Returns:
            Dict: Search results and generated response
        """
        data = {"query": query}

        if model:
            data["model"] = model
        if workspace_ids:
            data["workspace_ids"] = workspace_ids
        if file_ids:
            data["file_ids"] = file_ids
        if chat_session_id:
            data["chat_session_id"] = chat_session_id
        if company_scope is not None:
            data["company_scope"] = company_scope
        if private_scope is not None:
            data["private_scope"] = private_scope
        if tool:
            data["tool"] = tool

        return self._make_request("POST", "/api/v2/chat/document-search", data)


class DocumentSearchAlt(ParadigmAPI):
    """Alternative implementation for document search that breaks down the process into multiple steps.
    Used when document-search endpoint is not working."""

    def _query_documents(self, query: str, n: int = 5) -> List[Dict]:
        """
        Query documents using the query endpoint to get top N chunks.

        Args:
            query (str): The search query
            n (int, optional): Number of top chunks to retrieve

        Returns:
            List[Dict]: List of retrieved chunks with their metadata
        """
        data = {"query": query, "n": n}

        return self._make_request("POST", "/api/v2/query", data)

    def _generate_response(
        self, query: str, context_chunks: List[Dict], model: str = None
    ) -> tuple[Dict, List[Dict]]:
        """
        Generate a response using chat completions based on retrieved chunks.
        Also returns the messages list used as the prompt.

        Args:
            query (str): The original query
            context_chunks (List[Dict]): List of relevant text chunks
            model (str, optional): Model to use for completion

        Returns:
            tuple[Dict, List[Dict]]: A tuple containing the Chat completion response and the messages list sent to the API.
        """
        # Format context from chunks with explicit markers
        context_parts = []
        chunk_counter = 1
        for chunk_set in context_chunks:
            # Iterate through the actual chunks list within the chunk_set
            for chunk in chunk_set.get("chunks", []):
                if "text" in chunk:
                    source = chunk.get("metadata", {}).get("source", "N/A")
                    context_parts.append(
                        f"--- Start Chunk {chunk_counter} (Source: {source}) ---"
                    )
                    context_parts.append(chunk["text"])
                    context_parts.append(
                        f"--- End Chunk {chunk_counter} ---\n"
                    )  # Add extra newline for separation
                    chunk_counter += 1

        context = "\n".join(context_parts)

        # Create chat completion messages
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant. Use the provided context to answer the user's question. "
                "Only use information from the provided context. If you cannot answer based on the context, "
                "say so.",
            },
            {
                "role": "user",
                "content": f"QUESTION OF USER : {query}\n\nCONTEXT TO USE : {context}",
            },
        ]

        # Use ChatCompletions to generate response
        chat = ChatCompletions(self.api_key, self.base_url, self.default_model)
        response = chat.execute(messages, model)
        return response, messages  # Return both the response and the prompt messages

    def execute(
        self,
        query: str,
        model: str = None,
        workspace_ids: List[int] = None,
        company_scope: bool = None,
        private_scope: bool = None,
    ) -> Dict:
        """
        Execute the alternative document search process.

        Args:
            query (str): The search query
            model (str, optional): Model to use, defaults to the instance default model
            workspace_ids (List[int], optional): List of workspace IDs to search
            company_scope (bool, optional): Include documents from company collection
            private_scope (bool, optional): Include documents from private collection

        Returns:
            Dict: Generated response with context and the prompt used.
        """
        # Step 1: Query documents to get relevant chunks
        chunks = self._query_documents(query)

        if not chunks:
            return {"error": "No relevant content found via query endpoint."}

        # Step 2: Generate response using retrieved chunks
        response, prompt_messages = self._generate_response(query, chunks, model)

        # Return combined result
        return {
            "response": response,
            "source_chunks": chunks,
            "prompt_messages": prompt_messages,
        }


class Models(ParadigmAPI):
    """Implementation for models API endpoint."""

    def execute(self) -> List[str]:
        """
        Fetch and extract technical names from the LiveIntelligence API response.

        Returns:
            List[str]: List of technical names of available models.

        Raises:
            requests.RequestException: If the API request fails.
            ValueError: If the API response structure is invalid or JSON parsing fails.
        """
        response = self._make_request("GET", "/api/v2/models")

        # Simple check of expected structure
        if (
            not isinstance(response, dict)
            or "data" not in response
            or not isinstance(response["data"], list)
        ):
            raise ValueError("Invalid response structure received from API")

        technical_names = [
            model["technical_name"]
            for model in response["data"]
            if isinstance(model, dict) and "technical_name" in model
        ]

        return technical_names


class Files(ParadigmAPI):
    """Implementation for files API endpoint."""

    def execute(
        self,
        company_scope: Optional[bool] = None,
        private_scope: Optional[bool] = None,
        workspace_scope: Optional[int] = None,
        page: Optional[int] = None,
    ) -> List[Dict[str, str]]:
        """
        Retrieves files IDs + filenames from the LiveIntelligence API based on the specified scope.

        Args:
            company_scope (bool, optional): Include documents from company collection
            private_scope (bool, optional): Include documents from user's private collection
            workspace_scope (int, optional): Include documents from specific workspace ID
            page (int, optional): Page number for pagination

        Returns:
            List[Dict[str, str]]: List of dictionaries, each containing 'id' and 'filename' for a file.

        Raises:
            requests.RequestException: If the API request fails.
            ValueError: If the API response structure is invalid or JSON parsing fails.
        """
        params = {}

        # Add optional parameters if they are not None
        if company_scope is not None:
            params["company_scope"] = company_scope
        if private_scope is not None:
            params["private_scope"] = private_scope
        if workspace_scope is not None:
            params["workspace_scope"] = workspace_scope
        if page is not None:
            params["page"] = page

        response = self._make_request("GET", "/api/v2/files", params=params)

        # Extract only 'id' and 'filename' for each file
        filtered_files = [
            {"id": file_obj["id"], "filename": file_obj["filename"]}
            for file_obj in response.get("data", [])
            if "id" in file_obj and "filename" in file_obj
        ]

        return filtered_files
