steps:
  # npm login
  - task: PowerShell@2
    displayName: npm login
    inputs:
      targetType: inline
      pwsh: true
      script: |
        $FilePath = ".npmrc"

        # Test if file already exists
        if (-Not $(Test-Path $FilePath)) {
          # Create file
          New-Item -Path $FilePath -ItemType File

          #Check if file exists
          if (Test-Path $FilePath) {
            $registryName = "$env:CUSTOMNPMREGISTRYNAME"
            $registry = "$env:CUSTOMNPMREGISTRY"
            $registrybase = $registry.trimend("registry/") + "/" 

            Add-Content -Path $FilePath -Value "@$registryName`:registry=https:$registry"
            Add-Content -Path $FilePath -Value "@obs`:registry=https:$registry"
            Add-Content -Path $FilePath -Value "`nalways-auth=true`n"
            Add-Content -Path $FilePath -Value "; begin auth token"
            Add-Content -Path $FilePath -Value "$registry`:username=$env:CUSTOMNPMUSER"
            Add-Content -Path $FilePath -Value "$registry`:_password=$env:CUSTOMNPMPASSWORD"
            Add-Content -Path $FilePath -Value "$registry`:email=npm requires email to be set but doesn't use the value"
            Add-Content -Path $FilePath -Value "$registrybase`:username=$env:CUSTOMNPMUSER"
            Add-Content -Path $FilePath -Value "$registrybase`:_password=$env:CUSTOMNPMPASSWORD"
            Add-Content -Path $FilePath -Value "$registrybase`:email=npm requires email to be set but doesn't use the value"
            Add-Content -Path $FilePath -Value "; end auth token"
          }
        }
      workingDirectory: .
    env:
      CUSTOMNPMUSER: $(CustomNpmUser)
      CUSTOMNPMPASSWORD: $(CustomNpmPassword)
      CUSTOMNPMREGISTRYNAME: $(CustomNpmRegistryName)
      CUSTOMNPMREGISTRY: $(CustomNpmRegistry)
