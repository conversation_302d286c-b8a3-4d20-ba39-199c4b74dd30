<!DOCTYPE html>
<html dir="ltr" lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0" />
    <title>Stencil Component Starter</title>

    <script type="module" src="/build/obs-library.esm.js"></script>
    <script nomodule src="/build/obs-library.js"></script>
    <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet" />
  </head>
  <body>
    <button id="btn">Open chatbot</button>
    <obs-chatbot base-url="https://localhost:44394" api-version="api" provider="doc-search" />

    <script>
      const todoListElement = document.querySelector('obs-chatbot');
      const button = document.getElementById('btn');
      todoListElement.addEventListener('closedChatbot', event => {
        todoListElement.setAttribute('open', false);
      });
      button.onclick = () => {
        todoListElement.setAttribute('open', true);
      };
    </script>
  </body>
</html>
