'use client';

/**
 * This file was automatically generated by the Stencil React Output Target.
 * Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
 */

/* eslint-disable */

import { AutoScroll as AutoScrollElement, defineCustomElement as defineAutoScroll } from "@obs/obs-library/dist/components/auto-scroll.js";
import { LoaderDots as LoaderDotsElement, defineCustomElement as defineLoaderDots } from "@obs/obs-library/dist/components/loader-dots.js";
import { ObsChatbot as ObsChatbotElement, defineCustomElement as defineObsChatbot } from "@obs/obs-library/dist/components/obs-chatbot.js";
import type { EventName, StencilReactComponent } from '@stencil/react-output-target/runtime';
import { createComponent } from '@stencil/react-output-target/runtime';
import React from 'react';

export type AutoScrollEvents = NonNullable<unknown>;

export const AutoScroll: StencilReactComponent<AutoScrollElement, AutoScrollEvents> = /*@__PURE__*/ createComponent<AutoScrollElement, AutoScrollEvents>({
    tagName: 'auto-scroll',
    elementClass: AutoScrollElement,
    // @ts-ignore - React type of Stencil Output Target may differ from the React version used in the Nuxt.js project, this can be ignored.
    react: React,
    events: {} as AutoScrollEvents,
    defineCustomElement: defineAutoScroll
});

export type LoaderDotsEvents = NonNullable<unknown>;

export const LoaderDots: StencilReactComponent<LoaderDotsElement, LoaderDotsEvents> = /*@__PURE__*/ createComponent<LoaderDotsElement, LoaderDotsEvents>({
    tagName: 'loader-dots',
    elementClass: LoaderDotsElement,
    // @ts-ignore - React type of Stencil Output Target may differ from the React version used in the Nuxt.js project, this can be ignored.
    react: React,
    events: {} as LoaderDotsEvents,
    defineCustomElement: defineLoaderDots
});

export type ObsChatbotEvents = { onClosedChatbot: EventName<CustomEvent<any>> };

export const ObsChatbot: StencilReactComponent<ObsChatbotElement, ObsChatbotEvents> = /*@__PURE__*/ createComponent<ObsChatbotElement, ObsChatbotEvents>({
    tagName: 'obs-chatbot',
    elementClass: ObsChatbotElement,
    // @ts-ignore - React type of Stencil Output Target may differ from the React version used in the Nuxt.js project, this can be ignored.
    react: React,
    events: { onClosedChatbot: 'closedChatbot' } as ObsChatbotEvents,
    defineCustomElement: defineObsChatbot
});
