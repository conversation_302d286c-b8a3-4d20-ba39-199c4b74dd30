apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-front-poc-deployment
  labels:
    app.kubernetes.io/name: chatbot-front-poc
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: chatbot-front-poc
  template:
    metadata:
      labels:
        app.kubernetes.io/name: chatbot-front-poc
    spec:
      {{- if .Values.affinity }}
      affinity:
{{ toYaml .Values.affinity | nindent 8 }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: chatbot-front-poc
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: Always
          ports:
            - containerPort: {{ .Values.service.containerPort }}
          {{- with .Values.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
