
<!-- Auto Generated Below -->


## Overview

Display a chatbot

## Properties

| Property            | Attribute           | Description | Type                                              | Default                                             |
| ------------------- | ------------------- | ----------- | ------------------------------------------------- | --------------------------------------------------- |
| `apiVersion`        | `api-version`       |             | `string`                                          | `''`                                                |
| `baseUrl`           | `base-url`          |             | `string`                                          | `undefined`                                         |
| `company_scope`     | `company_scope`     |             | `boolean`                                         | `false`                                             |
| `errorMessage`      | `error-message`     |             | `string`                                          | `"I'm sorry, an error occured. Please, try later."` |
| `frequency_penalty` | `frequency_penalty` |             | `number`                                          | `0.5`                                               |
| `initialValue`      | `initial-value`     |             | `string`                                          | `''`                                                |
| `max_tokens`        | `max_tokens`        |             | `number`                                          | `1000`                                              |
| `model`             | `model`             |             | `string`                                          | `'llama-3-8b-instruct'`                             |
| `open`              | `open`              |             | `boolean`                                         | `undefined`                                         |
| `placeholder`       | `placeholder`       |             | `string`                                          | `'Type your message here...'`                       |
| `presence_penalty`  | `presence_penalty`  |             | `number`                                          | `0.0`                                               |
| `private_scope`     | `private_scope`     |             | `boolean`                                         | `true`                                              |
| `provider`          | `provider`          |             | `PROVIDER.DOCSEARCH \| PROVIDER.LIVEINTILLIGENCE` | `PROVIDER.LIVEINTILLIGENCE`                         |
| `startMessage`      | `start-message`     |             | `string`                                          | `"Hello ! I'm your virtual assistant !"`            |
| `stream`            | `stream`            |             | `boolean`                                         | `true`                                              |
| `systemMessage`     | `system-message`    |             | `string`                                          | `'You are a virtual assistant.'`                    |
| `temperature`       | `temperature`       |             | `number`                                          | `0.3`                                               |
| `theme`             | `theme`             |             | `THEME.AUTO \| THEME.DARK \| THEME.LIGHT`         | `THEME.AUTO`                                        |
| `tool`              | `tool`              |             | `string`                                          | `'DocumentSearch'`                                  |
| `top_p`             | `top_p`             |             | `number`                                          | `0.9`                                               |
| `workspace_ids`     | `workspace_ids`     |             | `number[]`                                        | `[1]`                                               |


## Events

| Event           | Description | Type               |
| --------------- | ----------- | ------------------ |
| `closedChatbot` |             | `CustomEvent<any>` |


## Dependencies

### Depends on

- [auto-scroll](../auto-scroll)
- [loader-dots](../loader-dots)

### Graph
```mermaid
graph TD;
  obs-chatbot --> auto-scroll
  obs-chatbot --> loader-dots
  style obs-chatbot fill:#f9f,stroke:#333,stroke-width:4px
```

----------------------------------------------

Build by OBS
