import { newSpecPage } from '@stencil/core/testing';
import { AutoScroll } from '../auto-scroll';

describe('auto-scroll', () => {
  it('renders', async () => {
    const page = await newSpecPage({
      components: [AutoScroll],
      html: `<auto-scroll></auto-scroll>`,
    });
    expect(page.root).toEqualHtml(`
      <auto-scroll>
        <mock:shadow-root>
          <div class="scroll-container">
            <slot></slot>
            <div></div>
          </div>
        </mock:shadow-root>
      </auto-scroll>
    `);
  });
});
