import { Marked } from 'marked';
import { markedHighlight } from 'marked-highlight';
import hljs from 'highlight.js/lib/core';
import json from 'highlight.js/lib/languages/json';
import csharp from 'highlight.js/lib/languages/csharp';
import javascript from 'highlight.js/lib/languages/javascript';
import typescript from 'highlight.js/lib/languages/typescript';
import php from 'highlight.js/lib/languages/php';
import python from 'highlight.js/lib/languages/python';
import DOMPurify from 'isomorphic-dompurify';

hljs.registerLanguage('json', json);
hljs.registerLanguage('csharp', csharp);
hljs.registerLanguage('javascript', javascript);
hljs.registerLanguage('typescript', typescript);
hljs.registerLanguage('php', php);
hljs.registerLanguage('python', python);

const marked = new Marked(
  markedHighlight({
    langPrefix: 'hljs language-',
    highlight(code) {
      return hljs.highlightAuto(code).value;
    },
  }),
  {
    renderer: {
      code(token) {
        return `<pre><div class="pre-header d-flex py-2 px-2">${token.lang ? token.lang : 'code'}</div><code class="hljs">${token.text}</code></pre>`;
      },
    },
  },
);

export const sanitizeComplexeSentence = (text: string) => DOMPurify.sanitize(marked.parse(text) as string, {});
export const sanitizeSentence = (text: string) => DOMPurify.sanitize(text);
export { marked };
