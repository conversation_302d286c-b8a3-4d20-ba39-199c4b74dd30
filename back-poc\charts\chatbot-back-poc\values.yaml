replicaCount: 1

image:
  repository: multirepo.orangeapplicationsforbusiness.com:5002/genai_saas_product/business-apps/chatbot/live-intelligence-chatbot-back
  tag: __BUILD_TAG__

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: worker_type
          operator: In
          values:
          - services

liveIntelligence:
  apiKey: test
  url: test
  model: alfred-4.1

imagePullSecrets:
  - name: dxstore-artifactory

service:
  containerPort: 8080
  ports:
    - name: https
      protocol: TCP
      port: 443
      targetPort: 8080

resources:
  limits:
    cpu: "2"
    memory: "500Mi"
    nvidia.com/gpu: "0"
  requests:
    cpu: "2"
    memory: "500Mi"
    nvidia.com/gpu: "0"
