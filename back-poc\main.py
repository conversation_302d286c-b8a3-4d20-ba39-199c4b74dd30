from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from api.live_api import DocumentSearch
import logging
import os
import sys

# Configure logging to output to the console with timestamp and level
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
)

REQUIRED_ENV_VARS = ["LIVE_INTELLIGENCE_API_KEY", "LIVE_INTELLIGENCE_URL", "LIVE_INTELLIGENCE_MODEL"]

missing_vars = [var for var in REQUIRED_ENV_VARS if not os.getenv(var)]
if missing_vars:
    logging.error(f"Missing required environment variables: {', '.join(missing_vars)}.")
    sys.exit(1)

API_KEY = os.environ['LIVE_INTELLIGENCE_API_KEY']
BASE_URL = os.environ["LIVE_INTELLIGENCE_URL"]
DEFAULT_MODEL = os.environ["LIVE_INTELLIGENCE_MODEL"]

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/health/live")
def liveness_probe():
    return JSONResponse(status_code=200, content={"status": "alive"})

@app.get("/health/ready")
def readiness_probe():
    return JSONResponse(status_code=200, content={"status": "ready"})

@app.post("/api/document-search")
async def search(request: Request):
    """
    Search documents using the Live Intelligence DocumentSearch API.
    Returns Content = {"role": "assistant", "content": answer} where answer is the grounded content of
    the query from both demo documents.

    Expected request (JSON):
        {
            "query": "What is the company's revenue?",
            "model": "your_model_name",
            "workspace_ids": [123],
            "file_ids": [456, 789],
            "chat_session_id": 42,
            "company_scope": true,
            "private_scope": false,
            "tool": "DocumentSearch"
        }

    CURL tester :
    Invoke-WebRequest -Uri "http://localhost:8080/document-search" -Method POST -Headers @{ "Content-Type" = "application/json" } -Body '{ "query": "Comment remplir le CERFA ?", "private_scope": true, "workspace_ids": [1] }'
    """
    logging.info("[API] Received /api/document-search request")
    try:
        body = await request.json()
        logging.info(f"[API] Request body: {body}")
        query = body["query"]
        model = body.get("model", DEFAULT_MODEL)
        workspace_ids = body.get("workspace_ids")
        file_ids = body.get("file_ids")
        chat_session_id = body.get("chat_session_id")
        company_scope = body.get("company_scope")
        private_scope = body.get("private_scope")
        tool = body.get("tool", "DocumentSearch")
    except Exception as e:
        logging.error(f"[API] Error parsing request: {e}")
        raise HTTPException(status_code=400, detail=f"Invalid request: {e}")

    search_api = DocumentSearch(api_key=API_KEY, base_url=BASE_URL, default_model=DEFAULT_MODEL)
    try:
        logging.info(f"[API] Executing DocumentSearch for query: '{query}'")
        result = search_api.execute(
            query=query,
            model=model,
            workspace_ids=workspace_ids,
            file_ids=file_ids,
            chat_session_id=chat_session_id,
            company_scope=company_scope,
            private_scope=private_scope,
            tool=tool
        )
        answer = result.get("answer", "")
        logging.info(f"[API] Search completed successfully. Returning answer of length {len(answer)}.")
        return {"role": "assistant", "content": answer}
    except Exception as e:
        logging.error(f"[API] Error during DocumentSearch execution: {e}")
        return JSONResponse(status_code=500, content={"error": str(e)})

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=8080, reload=True)
