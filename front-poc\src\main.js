import { createApp } from 'vue'
import App from './App.vue'
import './assets/main.css'
import { ObsLibrary } from '@obs/obs-vue-library'
import router from './router/index'
import mainOidc from './services/SecurityService'

mainOidc.startup().then(ok => {
    if (ok) {
    } else {
        console.error("Something went wrong");
    }
});

createApp(App)
    .use(ObsLibrary)
    .use(router)
    .mount('#app')