# Description

This backend exposes the /document-search endpoint via FastAPI.
It can be used to obtain a answer from the LiveIntelligence API grounded on documents.

Send a POST request to http://localhost:8081/document-search with the body specified in the Interface section.

# Interface

## Input

```
{
    "query": "Comment remplir le CERFA ?",
    "model": "alfred-4.1",
    "workspace_ids": [1], // Optional
    "chat_session_id": 42, // Optional
    "company_scope": true, // Optional
    "private_scope": false, // Optional
    "tool": "DocumentSearch" // Optional
}
```

## Output

```
{
    "role": "assistant",
    "content": "..."
}
```
The raw view:
```
RawContent        : HTTP/1.1 200 OK
                    Content-Length: 3165
                    Content-Type: application/json
                    Date: Tue, 06 May 2025 12:00:08 GMT
                    Server: uvicorn

                    {"role":"assistant","content":"Pour remplir le CERFA 12156*05, voici les ...
```