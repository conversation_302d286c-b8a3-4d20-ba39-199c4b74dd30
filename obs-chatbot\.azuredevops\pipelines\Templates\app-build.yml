# Job template - Build Web Project
parameters:
  webArtifactName: ""
  publishArtifact: false
  environment: "dev"
  backUrl: ""

steps:
  # Init variables in Powershell
  - template: ../Templates/initialization.yml

  - task: PowerShell@2
    displayName: Prepare app variables
    inputs:
      targetType: inline
      pwsh: true
      script: |
        Write-Host "##vso[task.setvariable variable=API_BASE_URL;]$env:API_BASE_URL"
        Write-Host "##vso[task.setvariable variable=API_VERSION;]$env:API_VERSION"
    env:
      API_BASE_URL: $(ApiBaseUrl)
      API_VERSION: $(ApiVersion)

  - task: PowerShell@2
    displayName: Read variables
    inputs:
      targetType: inline
      pwsh: true
      script: |
        Write-Host "$env:API_BASE_URL"
        Write-Host "$env:API_VERSION"

  # npm login
  - template: ../Templates/npmRegistryLogin.yml

  # Restore npm packages
  - task: PowerShell@2
    displayName: Restore yarn packages
    inputs:
      targetType: inline
      pwsh: true
      script: yarn --frozen-lockfile
      workingDirectory: ./obs-library/

  - task: PowerShell@2
    displayName: yarn build
    inputs:
      targetType: inline
      pwsh: true
      script: yarn build
      workingDirectory: ./obs-library/

  - task: ArchiveFiles@2
    condition: ${{ parameters.publishArtifact }}
    displayName: "Archive files"
    inputs:
      rootFolderOrFile: "$(System.DefaultWorkingDirectory)/dist"
      includeRootFolder: false
      archiveFile: "$(Build.ArtifactStagingDirectory)/${{ parameters.webArtifactName }}.zip"

  - task: PublishBuildArtifacts@1
    condition: ${{ parameters.publishArtifact }}
    inputs:
      PathtoPublish: $(Build.ArtifactStagingDirectory)/${{ parameters.webArtifactName }}.zip
      ArtifactName: ${{ parameters.webArtifactName }}
      publishLocation: "Container"
