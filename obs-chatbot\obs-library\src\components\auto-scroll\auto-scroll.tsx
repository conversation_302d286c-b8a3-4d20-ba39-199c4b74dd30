import { Component, Element, h, Prop, State, Watch } from '@stencil/core';
import { mdiArrowDown } from '@mdi/js';

@Component({
  tag: 'auto-scroll',
  styleUrl: 'auto-scroll.scss',
  shadow: true,
})
export class AutoScroll {
  @Element() el: HTMLElement;

  @Prop() scrollEnabled: boolean = true;
  @Prop() content: string = '';

  @State() localScrollTop: number = 0;
  @State() isAtBottom: boolean = true;
  @State() displayButton: boolean = false;

  private anchor: HTMLElement;
  private container: HTMLElement;
  private lastScrollTop: number = 0;

  componentWillLoad() {
    this.handleContentChange();
  }

  componentDidLoad() {
    if (this.container) {
      this.container.addEventListener('scroll', this.handleScroll);
    }
  }

  disconnectedCallback() {
    if (this.container) {
      this.container.removeEventListener('scroll', this.handleScroll);
    }
  }

  @Watch('content')
  handleContentChange() {
    if (this.scrollEnabled && this.isAtBottom) {
      this.scrollToBottom();
    }
  }

  private readonly scrollToBottom = () => {
    requestAnimationFrame(() => {
      if (this.anchor) {
        this.anchor.scrollIntoView({ behavior: 'smooth', block: 'end' });
      }
    });
  };

  private readonly handleScroll = (event: Event) => {
    const target = event.target as HTMLElement;
    const scrollTop = target.scrollTop;
    const scrollHeight = target.scrollHeight;
    const clientHeight = target.clientHeight;

    this.isAtBottom = scrollTop + clientHeight >= scrollHeight - 5;

    if (scrollTop > this.lastScrollTop) {
      this.displayButton = false;
    } else {
      this.displayButton = true;
    }

    this.lastScrollTop = scrollTop;
  };

  private readonly resumeAutoScroll = () => {
    this.isAtBottom = true;
    this.scrollToBottom();
  };

  render() {
    return (
      <div
        class="scroll-container"
        ref={el => {
          if (el) this.container = el;
        }}
      >
        <slot></slot>
        <div ref={el => (this.anchor = el)}></div>

        {this.displayButton && (
          <button class="scroll-button" onClick={this.resumeAutoScroll}>
            <svg-icon type="mdi" path={mdiArrowDown} />
          </button>
        )}
      </div>
    );
  }
}
