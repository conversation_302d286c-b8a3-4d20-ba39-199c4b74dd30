@use '../../global/utils.scss';

$obsLoaderBackgroud: var(--obs-loader-backgroud, #969696);
$obsLoaderColor: var(--obs-loader-color, #c2c2c2);

.chat-bubble {
  padding: 9px 15px;
  background-color: $obsLoaderBackgroud;
  border-radius: 20px;
  position: relative;
  font-size: 14px;
  line-height: 1.5;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .ball-1,
  .ball-2,
  .ball-3 {
    animation-name: bounce;
    animation-duration: 1.2s;
    animation-iteration-count: infinite;
    -webkit-animation-name: bounce;
    -webkit-animation-duration: 1.2s;
    -webkit-animation-iteration-count: infinite;
  }
  .ball-1 {
    animation-delay: 1s;
    -webkit-animation-delay: 1s;
  }
  .ball-2 {
    animation-delay: 0.5s;
    -webkit-animation-delay: 0.5s;
  }
  .ball-3 {
    animation-delay: 0.125s;
    -webkit-animation-delay: 0.125s;
  }
  .circle {
    background-color: $obsLoaderColor;
    height: 3px;
    width: 3px;
    margin: 1px;
    border-radius: 25px;
  }
}

@keyframes bounce {
  0% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(2px);
  }
  60% {
    transform: translateY(0px);
  }
  80% {
    transform: translateY(0);
  }
}
