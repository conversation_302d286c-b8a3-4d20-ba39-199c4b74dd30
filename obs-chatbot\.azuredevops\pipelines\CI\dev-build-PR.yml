# Variable 'scOrganization' was defined in the Variables tab : Organization name in SonarCloud
# Variable 'scProjectKey' was defined in the Variables tab : Project key in SonarCloud
# Variable 'scProjectName' was defined in the Variables tab : Project name in SonarCloud
# Variable 'scServiceConnection' was defined in the Variables tab : name of the Service connection set in Azure DevOps for the SonarCloud service.

#trigger on develop's PR
trigger: none

# Pool will execute jobs
pool:
  vmImage: $(vmImage)

variables:
  # Import shared variables
  - template: ../Templates/variables.yml

# Build number format
name: $(Build.DefinitionName)_$(SourceBranchName)-$(Date:yyyy-MM-dd)-build$(Rev:.rrr)

stages:
  # Run Sonar Analysis
  - stage: BuildAndAnalysis
    jobs:
      # Tests Api
      - job: codeanalysis
        continueOnError: false
        displayName: Analysis for security and code quality
        steps:
          - template: ../Templates/analyzeSonar.yml
            parameters:
              scServiceConnection: $(scServiceConnection)
              scOrganization: $(scOrganization)
              scProjectKey: $(scProjectKey)
              scProjectName: $(scProjectName)
              restorePackages: false
