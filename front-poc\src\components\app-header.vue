<template>
  <header>
    <div style="right: 20px; top: 20px; position: absolute">
      <button class="logBtn" @click="mainOidc.signIn()" v-if="!mainOidc.isAuthenticated">
        Log in
      </button>
      <p style="margin-bottom: 5px" v-if="mainOidc.isAuthenticated">
        {{ mainOidc.userProfile.name }}
      </p>
      <button class="logBtn" @click="mainOidc.signOut()" v-if="mainOidc.isAuthenticated">
        Log out
      </button>
    </div>
    <h1>Bienvenue à la Ville de Lampe</h1>
    <nav>
      <a :href="baseUrl">Home</a>
      <a :href="baseUrl + 'chatbot'">Chatbot</a>
    </nav>
  </header>
</template>

<script setup>
import mainOidc from "@/services/SecurityService";
const baseUrl = import.meta.env.BASE_URL;
</script>

<style scoped>
header {
  background: #ff7900;
  color: white;
  padding: 20px 0;
  text-align: center;
}

nav {
  margin: 20px 0;
}

nav a {
  margin: 0 15px;
  color: white;
  text-decoration: none;
}

.logBtn {
  background-color: #ff7900;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 16px;
  text-align: center;
  width: 100px;
  padding: 10px;
  border: 1px solid white;
  transition:
    background-color 0.25s ease-in,
    color 0.25s ease-in,
    box-shadow 0.25s ease-in;
}

.logBtn:hover {
  background-color: white;
  color: #ff7900;
  box-shadow:
    0 12px 16px 0 rgba(0, 0, 0, 0.24),
    0 17px 50px 0 rgba(0, 0, 0, 0.19);
}
</style>
