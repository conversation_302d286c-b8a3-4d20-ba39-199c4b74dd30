@use '../../global/grid.scss';
@use '../../global/utils.scss';
@use '../../../node_modules/highlight.js/styles/tokyo-night-dark.css';

:host {
  // Colors Light / Dark
  --obs-container-background-color: #ffffff;
  --obs-dark-container-background-color: #212121;
  --obs-text-color: #333333;
  --obs-dark-text-color: #ffffff;
  --obs-chat-human-background-color: #eeeeee;
  --obs-dark-chat-human-background-color: #494949;
  --obs-field-background-color: #ffffff;
  --obs-dark-field-background-color: #494949;
  --obs-icon-color: #333333;
  --obs-dark-icon-color: #ffffff;
  --obs-chat-pre-code: #c2c2c2;
  --obs-dark-chat-pre-code: #494949;

  // Components
  --obs-loader-color: #eeeeee;
  --obs-dark-loader-color: #ffffff;
  --obs-loader-backgroud: #bdbdbd;
  --obs-dark-loader-backgroud: #494949;
  --obs-auto-scroll-backgroud: #ffffff;
  --obs-dark-auto-scroll-backgroud: #333333;
  --obs-auto-scroll-color: #333333;
  --obs-dark-auto-scroll-color: #bdbdbd;

  // Utils
  --obs-container-width: 500px;
  --obs-scroll-color: #c2c2c2;
  --obs-primary-color: #007bff;
  --obs-font-size: 14px;

  font-size: var(--obs-font-size);
}

[data-theme='dark'] {
  // Apply dark mode
  --obs-container-background-color: var(--obs-dark-container-background-color);
  --obs-text-color: var(--obs-dark-text-color);
  --obs-chat-human-background-color: var(--obs-dark-chat-human-background-color);
  --obs-field-background-color: var(--obs-dark-field-background-color);
  --obs-icon-color: var(--obs-dark-icon-color);
  --obs-chat-pre-code: var(--obs-dark-chat-pre-code);
  --obs-auto-scroll-backgroud: var(--obs-dark-auto-scroll-backgroud);
  --obs-auto-scroll-color: var(--obs-dark-auto-scroll-color);

  // Components
  --obs-loader-color: var(--obs-dark-loader-color);
  --obs-loader-backgroud: var(--obs-dark-loader-backgroud);
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  z-index: 1;

  &-track {
    background: transparent;
  }
  &-thumb {
    background: var(--obs-scroll-color);
    opacity: 0.5;
    border-radius: 100px;
  }
}
html,
body * {
  scrollbar-color: var(--obs-scroll-color) transparent;
  scrollbar-width: thin;
}

svg-icon[type='mdi'] {
  color: var(--obs-icon-color);
}

.obs {
  &--container {
    font-family: 'Roboto', Arial, sans-serif;
    background-color: var(--obs-container-background-color);
    position: fixed;
    bottom: 0;
    width: var(--obs-container-width);
    right: 0;
    height: 100vh;
    z-index: 1008;
    transform: translateX(100%);
    transition: transform 0.3s ease, opacity 0.3s ease, visibility 0.3s ease;
    display: flex;
    flex-direction: column;

    @media (max-width: 500px) {
      width: 100%;
    }

    &.open {
      transform: translateX(0);
    }

    &-header {
      &-title {
        align-items: center;
      }
      &-close {
        border: 0;
        background-color: transparent;
        cursor: pointer;
      }
    }
  }
  &--prepend {
    flex: none;
    overflow: hidden;
  }
  &--content {
    flex: 0 1 auto;
    height: 100%;
    max-width: 100%;
    overflow-x: hidden;
    overflow-y: auto;
  }
  &--append {
    flex: none;
    overflow: hidden;
    position: relative;
  }
  &--scrim {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: 0.2;
    transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1007;

    &.open {
      display: block;
    }
  }

  /* Textfield style */
  &--textfield {
    &-container {
      box-sizing: border-box;
      width: 100%;
      border-radius: 8px;
      box-shadow: 0 4px 13px rgba(0, 0, 0, 0.1);
      background-color: var(--obs-field-background-color);
    }
    &-textarea {
      box-sizing: border-box;
      min-height: 1em;
      max-height: 200px;
      height: auto;
      width: 100%;
      padding: 8px;
      line-height: 1.5;
      border: none;
      outline: none;
      font-family: inherit;
      background-color: var(--obs-field-background-color);
      color: var(--obs-text-color);
      resize: none;
    }
    &-button-container {
      display: flex;
      justify-content: flex-end;
      gap: 8px; /* Spacing between buttons */
    }
    &-button {
      padding: 5px 16px;
      font-size: 14px;
      font-weight: 500;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      background-color: var(--obs-primary-color);
      color: #fff;
      transition: background-color 0.2s, box-shadow 0.2s;

      &[disabled] {
        cursor: default;
        background-color: #c4c4c4;
      }
      &-icon {
        border-radius: 100%;
        padding: 8px;
        & > svg-icon {
          color: #ffffff;
        }
      }
      &:not([disabled]):hover {
        filter: brightness(90%);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
    }
  }

  /*chat style */
  &--chat {
    &-container {
      display: flex;
      justify-content: end;
    }
    &-human {
      background-color: var(--obs-chat-human-background-color);
      border-radius: 4px;
      width: 100%;
      box-sizing: border-box;
    }
    &-bot,
    &-human {
      line-height: 1.2rem;
      color: var(--obs-text-color);
      width: 100%;
      pre {
        overflow-x: auto;
      }
    }
  }
}

pre div.pre-header {
  background-color: var(--obs-chat-pre-code);
  border-radius: 4px 4px 0 0;
  justify-content: space-between;
}
