# TODOLIST

Ne pas afficher Bonjour <PERSON>, mais juste <PERSON>jou<PERSON> : l’utilisateur ne sera pas identifié lorsqu’il l’utilisera.

Les sources sont cités à l’intérieur de la réponse => peut-on l’éviter ? Et mettre les sources juste à la fin ?

Y-a-t-il un pré-prompt (si oui lequel ?), et peut-on rajouter le vouvoiement ?

Peut-on rajouter dans la base documentaire un doc contenant le lien vers le cerfa afin que la réponse puisse y faire référence ?

Qui écrit le message de fin : « Alfred 4, ton copilot AI de confiance » Peut-on le modifier ?

« copilot AI » => « copilote IA » en français

        :system-message="'Tu es un assistant IA et tu réponds à des utilisateurs naviguant sur un site du service public Français. Tu réponds dans la langue de l\'utilisateur. Tu ne DOIS PAS saluer l\'utilisateur par son nom mais te limiter à dire Bonjour. Tu utilises exclusivement le vouvoiement. Tes sources, si elles apparaissent dans ta réponse, ne doivent êtres listées qu\'à la fin de la réponse et ne JAMAIS apparaître à l\'intérieur de ta réponse. A la fin de ta réponse, tu ne dois pas ajouter de formule de politesse ni t\'identifier comme ALfred 4.'"
