import axios, { AxiosInstance } from 'axios';

export default class APIInstance {
  public readonly api: AxiosInstance;

  constructor(apiUrl: string, apiVersion?: string) {
    this.api = axios.create({
      baseURL: apiVersion ? `${apiUrl}/${apiVersion}` : apiUrl,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.api.interceptors.request.use(
      config => {
        return config;
      },
      error => {
        console.error('Request error:', error);
        return Promise.reject(new Error(error));
      },
    );

    this.api.interceptors.response.use(
      response => {
        return response;
      },
      async error => {
        if (error.response?.status === 401) {
          console.warn('Token expired. Attempting to refresh...');
        }

        console.error('Response error:', error);
        return Promise.reject(new Error(error));
      },
    );
  }
}
