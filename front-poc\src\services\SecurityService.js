
import { createOidcAuth, SignInType } from 'vue-oidc-client/vue3';

const appUrl = import.meta.env.VITE_APP_HOST_NAME + import.meta.env.BASE_URL;

const mainOidc = createOidcAuth('main', SignInType.Window, appUrl, {
    authority: import.meta.env.VITE_APP_OPENID_AUTHORITY,
    client_id: import.meta.env.VITE_APP_OPENID_CLIENT_ID,
    response_type: 'code',
    scope: 'openid profile email',
    post_logout_redirect_uri: appUrl
});

export default mainOidc;