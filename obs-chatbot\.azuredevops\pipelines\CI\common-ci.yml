# Variable 'scOrganization' was defined in the Variables tab : Organization name in SonarCloud
# Variable 'scProjectKey' was defined in the Variables tab : Project key in SonarCloud
# Variable 'scProjectName' was defined in the Variables tab : Project name in SonarCloud
# Variable 'scServiceConnection' was defined in the Variables tab : name of the Service connection set in Azure DevOps for the SonarCloud service.

trigger:
  branches:
    include:
      - "*"
  paths:
    include:
      - .azuredevops/pipelines/

# Pool will execute jobs
pool:
  vmImage: $(vmImage)

variables:
  # Import shared variables
  - template: ../Templates/variables.yml

# Build number format
name: $(Build.DefinitionName)_$(SourceBranchName)-$(Date:yyyy-MM-dd)-build$(Rev:.rrr)

stages:
  - stage: Build
    condition: and(succeeded(), ne(variables['EnableSonarCloud'], 'true'))
    jobs:
      # Build Web Project
      - job: build_web
        dependsOn: [] # this removes the implicit dependency on previous stage and causes this to run in parallel
        displayName: Build Web Project
        steps:
          # build compiled project
          - template: ../Templates/app-build.yml

  # Run of unit tests
  - stage: Tests
    dependsOn: Build
    condition: and(succeeded(), ne(variables['EnableSonarCloud'], 'true'))
    jobs:
      # Tests web
      - job: web_tests
        displayName: Tests web Project and packages
        steps:
          - template: ../Templates/app-test.yml

  # Run Sonar Analysis
  - stage: Analysis
    condition: |
      and(
       eq(variables['EnableSonarCloud'], 'true'),
       in(dependencies.Build.result, 'Succeeded', 'SucceededWithIssues', 'Skipped'),
       in(dependencies.Tests.result, 'Succeeded', 'SucceededWithIssues', 'Skipped')
      )
    dependsOn:
      - Build
      - Tests
    jobs:
      - job: codeanalysis
        continueOnError: false
        displayName: Analysis for security and code quality
        steps:
          - template: ../Templates/analyzeSonar.yml
            parameters:
              scServiceConnection: $(scServiceConnection)
              scOrganization: $(scOrganization)
              scProjectKey: $(scProjectKey)
              scProjectName: $(scProjectName)
              restorePackages: false
              publishTestResult: true
