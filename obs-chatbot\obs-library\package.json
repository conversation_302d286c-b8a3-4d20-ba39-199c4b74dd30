{"name": "@obs/obs-library", "version": "10.0.0", "description": "Stencil Component Starter", "main": "dist/index.cjs.js", "module": "dist/index.js", "types": "dist/types/index.d.ts", "collection": "dist/collection/collection-manifest.json", "collection:main": "dist/collection/index.js", "unpkg": "dist/obs-library/obs-library.esm.js", "exports": {".": {"import": "./dist/obs-library/obs-library.esm.js", "require": "./dist/obs-library/obs-library.cjs.js"}, "./dist/*": {"import": "./dist/*", "types": "./dist/*"}, "./components/*": {"import": "./dist/components/*.js", "types": "./dist/components/*.d.ts"}, "./loader": {"import": "./loader/index.js", "require": "./loader/index.cjs", "types": "./loader/index.d.ts"}}, "repository": {"type": "git", "url": "https://github.com/ionic-team/stencil-component-starter.git"}, "files": ["dist/", "loader/"], "scripts": {"build": "yarn clean & stencil build", "dev": "stencil build --dev --watch --serve", "test": "stencil test --spec --coverage", "generate": "stencil generate", "generate:doc": "stencil docs", "clean": "shx rm -rf build dist & shx rm -rf loader & shx rm -rf www & shx echo Clean Done & shx echo \n", "prepare": "yarn build"}, "devDependencies": {"@faker-js/faker": "^9.7.0", "@jamescoyle/svg-icon": "^0.2.1", "@mdi/js": "^7.4.47", "@microsoft/fetch-event-source": "^2.0.1", "@stencil/core": "^4.31.0", "@stencil/react-output-target": "^1.0.2", "@stencil/sass": "^3.2.1", "@stencil/store": "^2.1.3", "@stencil/vue-output-target": "^0.10.7", "@types/jest": "^29.5.14", "@types/markdown-it": "^14.1.2", "@types/node": "^22.15.17", "axios": "^1.9.0", "axios-mock-adapter": "^2.1.0", "highlight.js": "^11.11.1", "isomorphic-dompurify": "^2.24.0", "jest": "^29.7.0", "jest-cli": "^29.7.0", "marked": "^15.0.11", "marked-highlight": "^2.2.1", "puppeteer": "^24.8.2", "rimraf": "^6.0.1", "shx": "^0.4.0"}, "license": "MIT", "packageManager": "yarn@1.22.22", "gitHead": "4744d8f3dbe899b4213e5b3887d444f016d4166b"}