apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatbot-back-poc-deployment
  labels:
    app.kubernetes.io/name: chatbot-back-poc
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: chatbot-back-poc
  template:
    metadata:
      labels:
        app.kubernetes.io/name: chatbot-back-poc
    spec:
      {{- if .Values.affinity }}
      affinity:
{{ toYaml .Values.affinity | nindent 8 }}
      {{- end }}
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      containers:
        - name: chatbot-back-poc
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: Always
          env:
            - name: LIVE_INTELLIGENCE_API_KEY
              valueFrom:
                secretKeyRef:
                  key: LIVE_INTELLIGENCE_API_KEY
                  name: back-poc-secret
            - name: LIVE_INTELLIGENCE_URL
              valueFrom:
                secretKeyRef:
                  key: LIVE_INTELLIGENCE_URL
                  name: back-poc-secret
            - name: LIVE_INTELLIGENCE_MODEL
              valueFrom:
                secretKeyRef:
                  key: LIVE_INTELLIGENCE_MODEL
                  name: back-poc-secret
          ports:
            - containerPort: {{ .Values.service.containerPort }}
          {{- with .Values.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
