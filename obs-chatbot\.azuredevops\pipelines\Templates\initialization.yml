parameters:
  restorePackages: true

steps:
  - task: PowerShell@2
    condition: ${{ parameters.restorePackages }}
    displayName: Prepare cache
    inputs:
      targetType: inline
      pwsh: true
      script: |
        Write-Host "YARN_CACHE_FOLDER = $(yarn_config_cache)"
        Write-Host "##vso[task.setvariable variable=YARN_CACHE_FOLDER;]$(yarn_config_cache)"

  - task: Cache@2
    condition: ${{ parameters.restorePackages }}
    displayName: Cache yarn packages
    inputs:
      key: '"yarn" | "$(Agent.OS)" | yarn.lock'
      restoreKeys: |
        "yarn" | "$(Agent.OS)"
        "yarn"
      path: $(YARN_CACHE_FOLDER)

  - task: NodeTool@0
    condition: ${{ parameters.restorePackages }}
    displayName: Use Node 22.X
    inputs:
      versionSpec: 22.X

  # install npm
  - task: Npm@1
    condition: ${{ parameters.restorePackages }}
    displayName: npm 10.9.0
    inputs:
      command: custom
      customCommand: install -g npm@10.9.0

  # install yarn
  - task: Npm@1
    condition: ${{ parameters.restorePackages }}
    displayName: npm install yarn
    inputs:
      command: custom
      customCommand: install -g yarn
