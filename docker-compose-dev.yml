services:
  front:
    build:
      context: ./front-poc
      dockerfile: Dockerfile
      secrets:
        - env
    volumes:
      - ./front-poc/src:/app/src
    ports:
      - "8080:8080"
    command: "npm run dev"

  back:
    build:
      context: ./back-poc
      dockerfile: Dockerfile
    volumes:
      - ./back-poc:/app
    ports:
      - "8081:8080"
    environment:
      - LIVE_INTELLIGENCE_API_KEY=MSO6ztmm.VN7FVpQ7LGdgvSOM1knKw7wF5GSgdPts
      - LIVE_INTELLIGENCE_URL=https://trust.pprd.liveintelligence.orange-business.com
      - LIVE_INTELLIGENCE_MODEL=alfred-4.1

secrets:
  env:
    file: ./.env.local
