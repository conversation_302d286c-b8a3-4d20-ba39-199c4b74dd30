# Job template - Build Web Project
parameters:
  scServiceConnection: ""
  scOrganization: ""
  scProjectKey: ""
  scProjectName: ""
  webArtifactName: ""
  publishArtifact: false

steps:
  # SonarQube (SonarCloud)
  - task: SonarCloudPrepare@3
    displayName: "Prepare analysis on SonarCloud"
    inputs:
      SonarCloud: ${{ parameters.scServiceConnection }}
      organization: ${{ parameters.scOrganization }}
      scannerMode: "CLI"
      configMode: "manual"
      cliProjectKey: ${{ parameters.scProjectKey }}
      cliProjectName: ${{ parameters.scProjectName }}
      cliProjectVersion: $(Build.BuildNumber)
      extraProperties: |
        sonar.sourceEncoding=UTF-8
        sonar.sources=obs-library/src
        sonar.tests=obs-library/src
        sonar.test.inclusions=obs-library/src/**/test/**/*
        sonar.typescript.lcov.reportPaths=obs-library/coverage/lcov.info
        sonar.javascript.lcov.reportPaths=obs-library/coverage/lcov.info
        sonar.exclusions=obs-library/src/**/test/**/*
        sonar.coverage.exclusions=obs-library/src/**/test/**/*,obs-library/src/core/types/**/*,obs-library/src/core/mock/**/*,obs-library/src/core/enums/**/*,obs-library/src/plugins/**/*

  # build compiled project
  - template: ../Templates/app-build.yml
    parameters:
      webArtifactName: ${{ parameters.webArtifactName }}
      publishArtifact: ${{ parameters.publishArtifact }}

  # build and run tests
  - template: ../Templates/app-test.yml

  # SonarQube (SonarCloud)
  - task: SonarCloudAnalyze@3
    condition: succeededOrFailed()
    displayName: "Run Code Analysis"
  - task: SonarCloudPublish@3
    condition: succeededOrFailed()
    displayName: "Publish Quality Gate Result"
    inputs:
      pollingTimeoutSec: "300"
