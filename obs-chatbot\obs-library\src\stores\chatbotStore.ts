class Store {
  private listeners: { [key: string]: ((value: any) => void)[] } = {};
  private state = {
    writingMessage: '',
    isWriting: false,
    isLoading: false,
    isError: false,
  };

  getState() {
    return this.state;
  }

  setState(newState: Partial<typeof this.state>) {
    Object.keys(newState).forEach(key => {
      if (this.state[key] !== newState[key]) {
        this.state[key] = newState[key];
        this.listeners[key]?.forEach(listener => listener(newState[key]));
      }
    });
  }

  subscribe<K extends keyof typeof this.state>(key: K, listener: (value: (typeof this.state)[K]) => void) {
    if (!this.listeners[key]) {
      this.listeners[key] = [];
    }
    this.listeners[key].push(listener);

    return () => {
      this.listeners[key] = this.listeners[key].filter(l => l !== listener);
    };
  }
}

export const store = new Store();
