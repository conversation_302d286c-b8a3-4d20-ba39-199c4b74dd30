# Variable 'npmPackageName' was definied in the variables tab : npm package name to check

#trigger on develop's PR
trigger: none

# Pool will execute jobs
pool:
  vmImage: $(vmImage)

variables:
  # Import shared variables
  - template: ../Templates/variables.yml

# Build number format
name: $(Build.DefinitionName)_$(SourceBranchName)-$(Date:yyyy-MM-dd)-build$(Rev:.rrr)

stages:
  # Check package version
  - stage: CheckPackageVersion
    jobs:
      # Tests Api
      - job: checkNpmRegistryVersion
        continueOnError: false
        displayName: Check npm registry version
        steps:
          # Login npm registry
          - template: ../Templates/npmRegistryLogin.yml

          - task: PowerShell@2
            displayName: "Compare npm version"
            inputs:
              targetType: inline
              pwsh: true
              script: |
                $actualVersion = node -p "require('./package.json').version"
                $npmPackagesVersionObject = npm view $(npmPackageName) versions
                $packageVersions = [String]::Join('', $npmPackagesVersionObject).TrimStart('[').TrimEnd(']').Replace("'", "").Split(',')
                $packageVersionsTrimmed=@()
                Foreach($elem in $packageVersions) {
                  $packageVersionsTrimmed += $elem.Trim()
                }
                $versionExist = $packageVersionsTrimmed.Contains($actualVersion)
                if($versionExist) {
                  return Write-Error "The npm version already exist."
                }
