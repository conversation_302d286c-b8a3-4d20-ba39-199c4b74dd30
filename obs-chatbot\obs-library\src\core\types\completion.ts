import type { IMessage, IMessageChoice } from './message';

interface ICompletionRequest {
  model: string;
  messages: Array<IMessage>;
  temperature: number;
  max_tokens: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
  stream: boolean;
  workspace_ids?: Array<number>;
  chat_session_id?: number;
  company_scope?: boolean;
  private_scope?: boolean;
  tool?: string;
}

interface ICompletionResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<IMessageChoice>;
  usage: {
    prompt_tokens: number;
    total_tokens: number;
    completion_tokens: number;
  };
  prompt_logprobs: null;
}

export type { ICompletionRequest, ICompletionResponse };
