import { AxiosInstance } from 'axios';
import { ICompletionRequest } from './completion';
import { IMessage } from './message';

export interface ILiveIntelligenceCompletionRequest {
  model: string;
  messages: Array<IMessage>;
  temperature: number;
  max_tokens: number;
  top_p: number;
  frequency_penalty: number;
  presence_penalty: number;
  stream: boolean;
}
export interface IDocSearchCompletionRequest {
  query: string;
  model: string;
  workspace_ids?: Array<number>;
  company_scope?: boolean;
  private_scope?: boolean;
  tool?: string;
}

export interface IDocSearchCompletionResponse {
  role: string;
  content: string;
}

export interface Strategy {
  chat(apiInstance: AxiosInstance, input: ICompletionRequest): Promise<void>;
}
