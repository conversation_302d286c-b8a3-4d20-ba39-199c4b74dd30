/* eslint-disable */
/* tslint:disable */
/* auto-generated vue proxies */
import { defineContainer, type StencilVueComponent } from '@stencil/vue-output-target/runtime';

import type { JSX } from '@obs/obs-library/loader';




export const AutoScroll: StencilVueComponent<JSX.AutoScroll> = /*@__PURE__*/ defineContainer<JSX.AutoScroll>('auto-scroll', undefined, [
  'scrollEnabled',
  'content'
]);


export const LoaderDots: StencilVueComponent<JSX.LoaderDots> = /*@__PURE__*/ defineContainer<JSX.LoaderDots>('loader-dots', undefined);


export const ObsChatbot: StencilVueComponent<JSX.ObsChatbot> = /*@__PURE__*/ defineContainer<JSX.ObsChatbot>('obs-chatbot', undefined, [
  'baseUrl',
  'apiVersion',
  'open',
  'initialValue',
  'theme',
  'placeholder',
  'startMessage',
  'errorMessage',
  'systemMessage',
  'model',
  'temperature',
  'max_tokens',
  'top_p',
  'frequency_penalty',
  'presence_penalty',
  'stream',
  'provider',
  'workspace_ids',
  'company_scope',
  'private_scope',
  'tool',
  'closedChatbot'
], [
  'closedChatbot'
]);

