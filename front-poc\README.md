# LiveIntelligence-chatbot

LiveIntelligence-chatbot is a simple vue website developed as a Proof Of Concept for the deployment of the chatbot JS component developed by OBS

## Working on the project

To work on the project, clone the repository using the following command:

```sh
git clone https://git-oab.si.fr.intraorange/genai-product/development/chatbot.git
```

## Test the image

You can build the docker image yourself by following the steps below:

### Prerequisites

Ensure you have `docker` installed on your machine.

### Setup your environment

Before building the docker image, copy the `.env` file to `.env.local` and replace the placeholder values with your actual environment variables.

### Build the docker image

Once this is done, in order to build the docker image locally, run the following command:
```sh
docker build --secret id=env,src=.env.local -t lichatbot:latest .
```

### Run the docker image

The image exposes port 8080 to access its content.
To run it, run the following command :
```sh
docker run -p 8080:8080 lichatbot:latest
```

## CI/CD

This project comes with a CI/CD pipeline using GitLab CI. The pipeline is defined in the `.gitlab-ci.yml` file and includes stages for building, testing, and publishing the docker image to our Artifactory instance.

### Pipeline Stages

This pipeline contains four stages, that are executed as described in the following sub-sections :

#### Setup Stage

This is a preparatory stage that sets up the necessary environment and dependencies for the subsequent stages.

It accesses our Vault instance to retrieve secrets and environment variables needed for the build and push stages.

This stage runs every time the pipeline is triggered (commit, merge request, etc.)

#### Build Stage

This stage compiles the source code and builds the docker image inside the runner.

It is only meant as a way to check that the code compiles and the docker image is built successfully.

This stage does not deploy the image to any registry or environment.

As such, this stage runs every time the pipeline is triggered (commit, merge request, etc.)

#### Test Stage

This stage runs security tests, using trivy and xray to check for vulnerabilities in the dependencies used in the project.

##### Trivy Scan

This job scans the project's dependencies for known vulnerabilities and generates a report.

It runs under the following conditions :

- when a commit is made on the `main` or `dev` branch
- when a merge request is created targeting the `main` or `dev` branch.
- when a tag is created

##### Xray Scan

This job scans the built docker image for known vulnerabilities and generates a report.

It runs under the following conditions :

- when a commit is made on the `main` or `dev` branch
- when a merge request is created targeting the `main` or `dev` branch.
- when a tag is created

##### Push Stage

This stage builds and pushes the docker image to our Artifactory instance.

It assigns two tags to the image: `latest` and the current commit tag.

It only runs when a tag is positionned.