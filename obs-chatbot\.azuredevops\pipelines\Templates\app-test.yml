parameters:
  restorePackages: true

steps:
  # Init variables in Powershell
  - template: ../Templates/initialization.yml
    parameters:
      restorePackages: ${{ parameters.restorePackages }}

  # Restore npm packages
  - task: PowerShell@2
    condition: ${{ parameters.restorePackages }}
    displayName: Restore yarn packages
    inputs:
      targetType: inline
      pwsh: true
      script: yarn --frozen-lockfile
      workingDirectory: ./obs-library/

  - task: PowerShell@2
    displayName: yarn test
    inputs:
      targetType: inline
      pwsh: true
      script: yarn test
      workingDirectory: ./obs-library/

  - task: PublishCodeCoverageResults@2
    displayName: "Publish Cobertura code coverage"
    inputs:
      codeCoverageTool: Cobertura
      summaryFileLocation: "./obs-library/coverage/cobertura-coverage.xml"
