import { execSync } from "child_process";
import * as fs from "fs";
import * as path from "path";
import * as readline from "readline";

type Project = {
  name: string;
  path: string;
};

const principalProject: Project = {
  name: "@obs/obs-library",
  path: "./obs-library",
};

const projects: Project[] = [
  principalProject,
  { name: "@obs/obs-vue-library", path: "./obs-vue-library" },
  { name: "@obs/obs-react-library", path: "./obs-react-library" },
];

function getPublishedVersion(pkgName: string): string {
  try {
    const version = execSync(`yarn info ${pkgName} version`, {
      encoding: "utf-8",
    });
    return version.trim();
  } catch (error) {
    return "Not published";
  }
}

function askQuestion(query: string): Promise<string> {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(query, (answer) => {
      rl.close();
      resolve(answer);
    });
  });
}

function updatePackageVersion(pkgPath: string, version: string) {
  const pkgJsonPath = path.join(pkgPath, "package.json");
  const pkg = JSON.parse(fs.readFileSync(pkgJsonPath, "utf-8"));
  pkg.version = version;
  fs.writeFileSync(pkgJsonPath, JSON.stringify(pkg, null, 2));
}

function runCommand(command: string, cwd: string) {
  execSync(command, { stdio: "inherit", cwd });
}

function getLocalVersion(pkgPath: string): string {
  const pkgJsonPath = path.join(pkgPath, "package.json");
  const pkg = JSON.parse(fs.readFileSync(pkgJsonPath, "utf-8"));
  return pkg.version;
}

async function main() {
  let libVersion = "";
  for (let i = 0; i < projects.length; i++) {
    const { name, path: pkgPath } = projects[i];

    const publishedVersion = getPublishedVersion(name);
    const localVersion = getLocalVersion(pkgPath);

    console.log(`\n📦 ${name}`);
    console.log(`   🔸 Published version : ${publishedVersion}`);
    console.log(`   🔹 Local version   : ${localVersion}`);

    // If local version > published => default choice
    let suggestedVersion = localVersion;
    if (
      publishedVersion !== "Not published" &&
      localVersion <= publishedVersion
    ) {
      suggestedVersion = "";
    }

    const prompt = suggestedVersion
      ? `👉 New version publised for ${name} [${suggestedVersion}] : `
      : `👉 New version publised for ${name} : `;
    const input = await askQuestion(prompt);
    const newVersion = input.trim() || suggestedVersion;

    if (!/^\d+\.\d+\.\d+$/.test(newVersion)) {
      console.error(
        "❌ Invalid version. Use format MAJOR.MINOR.PATCH (ex: 1.2.3)"
      );
      process.exit(1);
    }

    updatePackageVersion(pkgPath, newVersion);

    if (i === 0) {
      libVersion = newVersion;
    } else {
      console.log(
        `⬇️  Installation of ${principalProject.name}@${libVersion} in ${name}...`
      );
      runCommand(`yarn add ${principalProject.name}@${libVersion}`, pkgPath);
    }

    console.log(`🚀 Publication of ${name}...`);
    runCommand(
      `yarn publish --new-version ${newVersion} --non-interactive`,
      pkgPath
    );
  }

  console.log("\n✅ All packages are published !");
}

main();
