apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: chatbot-front-poc-ingress
  annotations:
    cert-manager.io/issuer: "letsencrypt-liveintelligence"
    nginx.org/rewrites: serviceName=chatbot-front-poc rewrite=/chatbot
  labels:
    app: chatbot-front-poc
spec:
  ingressClassName: nginx
  tls:
    - hosts:
      - trust.pprd.liveintelligence.orange-business.com
      secretName: chatbot.liveintelligence.orange-business.com-tls
  rules:
    - host: trust.pprd.liveintelligence.orange-business.com
      http:
        paths:
          - pathType: Prefix
            path: /chatbot
            backend:
              service:
                name: chatbot-front-poc
                port:
                  number: 8080
