/* eslint-disable */
/* tslint:disable */
/**
 * This is an autogenerated file created by the Stencil compiler.
 * It contains typing information for all components that exist in this project.
 */
import { HTMLStencilElement, JSXBase } from "@stencil/core/internal";
import { THEME } from "./core/enums/theme";
import { PROVIDER } from "./core/enums/provider";
export { THEME } from "./core/enums/theme";
export { PROVIDER } from "./core/enums/provider";
export namespace Components {
    interface AutoScroll {
        "content": string;
        "scrollEnabled": boolean;
    }
    interface LoaderDots {
    }
    /**
     * Display a chatbot
     */
    interface ObsChatbot {
        "apiVersion": string;
        "baseUrl": string;
        "company_scope": boolean;
        "errorMessage": string;
        "frequency_penalty": number;
        "initialValue": string;
        "max_tokens": number;
        "model": string;
        "open": boolean;
        "placeholder": string;
        "presence_penalty": number;
        "private_scope": boolean;
        "provider": PROVIDER;
        "startMessage": string;
        "stream": boolean;
        "systemMessage": string;
        "temperature": number;
        "theme": THEME;
        "tool": string;
        "top_p": number;
        "workspace_ids": number[];
    }
}
export interface ObsChatbotCustomEvent<T> extends CustomEvent<T> {
    detail: T;
    target: HTMLObsChatbotElement;
}
declare global {
    interface HTMLAutoScrollElement extends Components.AutoScroll, HTMLStencilElement {
    }
    var HTMLAutoScrollElement: {
        prototype: HTMLAutoScrollElement;
        new (): HTMLAutoScrollElement;
    };
    interface HTMLLoaderDotsElement extends Components.LoaderDots, HTMLStencilElement {
    }
    var HTMLLoaderDotsElement: {
        prototype: HTMLLoaderDotsElement;
        new (): HTMLLoaderDotsElement;
    };
    interface HTMLObsChatbotElementEventMap {
        "closedChatbot": any;
    }
    /**
     * Display a chatbot
     */
    interface HTMLObsChatbotElement extends Components.ObsChatbot, HTMLStencilElement {
        addEventListener<K extends keyof HTMLObsChatbotElementEventMap>(type: K, listener: (this: HTMLObsChatbotElement, ev: ObsChatbotCustomEvent<HTMLObsChatbotElementEventMap[K]>) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | AddEventListenerOptions): void;
        addEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | AddEventListenerOptions): void;
        removeEventListener<K extends keyof HTMLObsChatbotElementEventMap>(type: K, listener: (this: HTMLObsChatbotElement, ev: ObsChatbotCustomEvent<HTMLObsChatbotElementEventMap[K]>) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof DocumentEventMap>(type: K, listener: (this: Document, ev: DocumentEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener<K extends keyof HTMLElementEventMap>(type: K, listener: (this: HTMLElement, ev: HTMLElementEventMap[K]) => any, options?: boolean | EventListenerOptions): void;
        removeEventListener(type: string, listener: EventListenerOrEventListenerObject, options?: boolean | EventListenerOptions): void;
    }
    var HTMLObsChatbotElement: {
        prototype: HTMLObsChatbotElement;
        new (): HTMLObsChatbotElement;
    };
    interface HTMLElementTagNameMap {
        "auto-scroll": HTMLAutoScrollElement;
        "loader-dots": HTMLLoaderDotsElement;
        "obs-chatbot": HTMLObsChatbotElement;
    }
}
declare namespace LocalJSX {
    interface AutoScroll {
        "content"?: string;
        "scrollEnabled"?: boolean;
    }
    interface LoaderDots {
    }
    /**
     * Display a chatbot
     */
    interface ObsChatbot {
        "apiVersion"?: string;
        "baseUrl"?: string;
        "company_scope"?: boolean;
        "errorMessage"?: string;
        "frequency_penalty"?: number;
        "initialValue"?: string;
        "max_tokens"?: number;
        "model"?: string;
        "onClosedChatbot"?: (event: ObsChatbotCustomEvent<any>) => void;
        "open"?: boolean;
        "placeholder"?: string;
        "presence_penalty"?: number;
        "private_scope"?: boolean;
        "provider"?: PROVIDER;
        "startMessage"?: string;
        "stream"?: boolean;
        "systemMessage"?: string;
        "temperature"?: number;
        "theme"?: THEME;
        "tool"?: string;
        "top_p"?: number;
        "workspace_ids"?: number[];
    }
    interface IntrinsicElements {
        "auto-scroll": AutoScroll;
        "loader-dots": LoaderDots;
        "obs-chatbot": ObsChatbot;
    }
}
export { LocalJSX as JSX };
declare module "@stencil/core" {
    export namespace JSX {
        interface IntrinsicElements {
            "auto-scroll": LocalJSX.AutoScroll & JSXBase.HTMLAttributes<HTMLAutoScrollElement>;
            "loader-dots": LocalJSX.LoaderDots & JSXBase.HTMLAttributes<HTMLLoaderDotsElement>;
            /**
             * Display a chatbot
             */
            "obs-chatbot": LocalJSX.ObsChatbot & JSXBase.HTMLAttributes<HTMLObsChatbotElement>;
        }
    }
}
