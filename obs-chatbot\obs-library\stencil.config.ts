import { Config } from '@stencil/core';
import { sass } from '@stencil/sass';
import { reactOutputTarget } from '@stencil/react-output-target';
import { vueOutputTarget } from '@stencil/vue-output-target';

export const config: Config = {
  namespace: 'obs-library',
  outputTargets: [
    {
      type: 'dist',
      esmLoaderPath: '../loader',
    },
    {
      type: 'dist-custom-elements',
      customElementsExportBehavior: 'auto-define-custom-elements',
      externalRuntime: false,
    },
    {
      type: 'docs-readme',
      footer: 'Build by OBS',
      dir: 'docs',
    },
    {
      type: 'www',
      serviceWorker: null, // disable service workers
    },
    reactOutputTarget({
      outDir: '../obs-react-library/lib/components/stencil-generated/',
    }),
    vueOutputTarget({
      componentCorePackage: '@obs/obs-library/loader',
      proxiesFile: '../obs-vue-library/lib/components.ts',
    }),
  ],
  plugins: [sass()],
  testing: {
    collectCoverage: true,
    coverageReporters: ['lcov', 'html', 'cobertura'],
    collectCoverageFrom: ['src/**/*.{ts,tsx}'],
    coveragePathIgnorePatterns: ['src/core/enums', 'src/plugins'],
    moduleNameMapper: {
      '\\.(css|less|scss)$': '<rootDir>/src/core/mock/styleMock.ts',
    },
    setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  },
  extras: {
    enableImportInjection: true,
  },
};
