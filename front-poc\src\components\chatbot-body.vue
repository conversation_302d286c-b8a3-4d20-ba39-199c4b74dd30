<template>
  <div class="container">
    <div class="hero">
      <h1>Votre ville, votre avenir</h1>
    </div>

    <div class="container">
      <div class="content">
        <h2>À propos de nous</h2>
        <p>
          La Ville de Lampe est dédiée à offrir un cadre de vie agréable et
          dynamique à ses habitants. Découvrez nos services, événements et
          projets en cours.
        </p>
        <button @click="toggleChatbot">Chatbot</button>
        <obs-chatbot
        :open="open" @closedChatbot="toggleChatbot"
        :baseUrl.prop="backendHostName"
        :apiVersion.prop="backendApiPath"
        :provider="'doc-search'"
        :request-timeout="50000"
        :company_scope="false"
        :private_scope="true"
        :theme="'dark'"
        :key="obs - chatbot"
        :model.prop="modelName"
        :start-message="'Bon<PERSON><PERSON>, je suis l’assistant virtuel de la ville de Paris, propulsé par l’intelligence artificielle. Je peux vous aider à trouver des informations officielles sur l’urbanisme, les démarches administratives ou les services municipaux.'"
        :placeholder="'Poser votre question...'"
        :system-message="'Message sytème'"
        >
          <template #icone>
            <img src="/static/LI_Noir-Orange.png" alt="Live Intelligence Logo" class="custom-chatbot-icon" />
          </template>
        </obs-chatbot>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from "vue";

export default {
  setup() {
    const backendHostName = import.meta.env.VITE_APP_BACKEND_HOST_NAME;
    const backendApiPath = import.meta.env.VITE_APP_BACKEND_API_PATH;
    const modelName = import.meta.env.VITE_APP_MODEL_NAME;

    const open = ref(false);

    const toggleChatbot = () => {
      open.value = !open.value;
    };

    return {
      open,
      toggleChatbot,
      backendHostName,
      backendApiPath,
      modelName,
    };
  },
};
</script>

<style scoped>
.container {
  width: 100%;
  margin: auto;
  overflow: hidden;
  padding: 20px;
  box-sizing: border-box;
  flex: 1;
}

.hero h1 {
  font-size: 3em;
  text-align: center;
  margin-top: 50px;
  color: #ff7900;
}

.content {
  padding: 20px;
  background: white;
  margin-top: 20px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

obs-chatbot {
  /*--obs-dark-container-background-color: rgb(230, 230, 123);*/
  /*--obs-container-width: 800px;*/
  --obs-dark-icon-color: rgb(255, 140, 0);
  --obs-primary-color: rgb(255, 140, 0);
}

.custom-chatbot-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

</style>
